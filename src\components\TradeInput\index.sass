@import '@/assets/css/helpers.sass'
.container
  display: flex
  align-items: center
  font-size: px(15)
  margin-right: px(15)
  .leftlabel
    color: rgb(255, 255, 255)
    display: inline-block
    width: px(100)
  .myinput
    width: px(124)
    height: px(37)
    border-radius: px(5)
    border-width: px(0)
    background-color: rgb(26, 62, 112)
    color: rgb(148, 239, 255)
    text-align: center
    box-shadow: inset 0 0 px(10) px(5) rgba(159, 202, 252, 0.25)
    &:focus
        background-color: #0C203D
        border-color: white
        border-width: px(1)
        box-shadow: none
        outline: none
  .myinput::placeholder
    color: rgb(148, 239, 255)
    opacity: 1
    text-align: center
      
 