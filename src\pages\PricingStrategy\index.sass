@import '@/assets/css/helpers.sass'

.container
.dateRange
  margin-bottom: px(20)
  display: flex
  align-items: center
  gap: px(10)
  :global
    .ant-picker
      width: px(260)
      padding: px(3) px(10) !important
      background: rgba(0,0,0,0)
      border: white solid px(1)
      line-height: 0
      height: px(34) !important
    .ant-picker-input >input,.ant-picker-separator,.ant-picker-suffix,
    .ant-picker .ant-picker-input >input::placeholder
      color: white !important
      font-size: px(16)
.exportBtn
  line-height: px(28)
.gridLayout
  flex: 1
  display: grid
  grid-template-columns: 1fr
  grid-template-rows: 1fr 1fr
  gap: px(20)
.gridItem
  background: rgba(255, 255, 255, 0.05)
  border: 1px solid rgba(255, 255, 255, 0.1)
  border-radius: px(8)
  padding: px(10)
  display: flex
  flex-direction: column
  min-height: 0