
.box
    border-top: 2px solid #173e4f
    border-bottom: 2px solid #173e4f
    // @include corner-point
    .header
        border-bottom: 1px solid #173e4f
        display: flex
        justify-content: space-between
        align-items: center
        padding: 5px 10px
        height:50px
        .left
            display: flex
            align-items: center
            color: #fff
            .icon
                padding: 5px 5px
                padding-bottom: 0
                margin-right: 5px
                border: 1px solid #183847
            .title
                // @include text-gradient(#c4d4dc, #749cb8)
                font-size: 30px
                margin: 20px 0
    .content
        padding: 10px
        // padding-bottom: px(50)
        height: calc( 100% - 50px )
