import CustomInput from '@/components/CustomInput';
import React from 'react';
import styles from './index.sass';

// Extend InputGroupProps to include a placeholder property
interface InputGroupProps {
  left: string;
  value?: number; // Make value optional
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void; // Define the type for onChange
  placeholder?: string; // Make placeholder optional
}

const TradeInPut: React.FC<InputGroupProps> = ({
  left,
  value,
  onChange,
  placeholder,
}) => {
  return (
    <div className={styles.container}>
      <span className={styles.leftlabel}>{left}</span>
      {/* Use the placeholder prop in CustomInput */}
      <CustomInput
        className={styles.myinput}
        value={value}
        onChange={onChange}
        placeholder={placeholder || '请输入'}
      />
    </div>
  );
};

export default TradeInPut;
