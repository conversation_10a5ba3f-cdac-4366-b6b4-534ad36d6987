<svg width="114.000000" height="125.000000" viewBox="0 0 114 125" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_54_276_dd" x="23.506226" y="55.708740" width="66.987549" height="30.499756" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="3.98681" result="effect_layerBlur_1"/>
		</filter>
		<linearGradient x1="56.999996" y1="101.087173" x2="56.999996" y2="124.999931" id="paint_linear_54_273_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#001633"/>
			<stop offset="1.000000" stop-color="#00375E"/>
		</linearGradient>
		<linearGradient x1="56.999996" y1="81.766846" x2="56.999996" y2="124.999924" id="paint_linear_54_273_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
			<stop offset="1.000000" stop-color="#8CF1FF" stop-opacity="0.701961"/>
		</linearGradient>
		<linearGradient x1="56.999996" y1="88.869156" x2="56.999996" y2="112.781914" id="paint_linear_54_274_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#001633"/>
			<stop offset="1.000000" stop-color="#00375E"/>
		</linearGradient>
		<linearGradient x1="56.999996" y1="69.548828" x2="56.999996" y2="112.781906" id="paint_linear_54_274_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#9AF7FF" stop-opacity="0.101961"/>
			<stop offset="1.000000" stop-color="#8CF1FF" stop-opacity="0.701961"/>
		</linearGradient>
		<linearGradient x1="56.999996" y1="77.591080" x2="56.999996" y2="101.503838" id="paint_linear_54_275_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#054DA8"/>
			<stop offset="1.000000" stop-color="#62B7F2"/>
		</linearGradient>
		<linearGradient x1="56.999996" y1="58.270752" x2="56.999996" y2="101.503830" id="paint_linear_54_275_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#9AF7FF" stop-opacity="0.247059"/>
			<stop offset="1.000000" stop-color="#8CF1FF"/>
		</linearGradient>
		<linearGradient x1="57.422203" y1="0.000000" x2="57.422203" y2="55.670887" id="paint_linear_54_277_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#47B7FF"/>
			<stop offset="0.999648" stop-color="#003EFF" stop-opacity="0.000000"/>
		</linearGradient>
		<linearGradient x1="57.422211" y1="0.000000" x2="57.422211" y2="65.437523" id="paint_linear_54_277_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#EEF9FF"/>
			<stop offset="1.000000" stop-color="#D9F0FF" stop-opacity="0.000000"/>
		</linearGradient>
		<linearGradient x1="57.422424" y1="26.553162" x2="57.422424" y2="41.870651" id="paint_linear_54_279_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#D6F4FF"/>
			<stop offset="1.000000" stop-color="#2085FF"/>
		</linearGradient>
		<linearGradient x1="58.000000" y1="20.885679" x2="58.000000" y2="59.413139" id="paint_linear_54_271_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#D6F4FF"/>
			<stop offset="1.000000" stop-color="#2085FF"/>
		</linearGradient>
	</defs>
	<rect id="组件 3" width="114.000000" height="125.000000" fill="#FFFFFF" fill-opacity="0"/>
	<path id="多边形备份 3" d="M57.78 82.06L108.57 101.32C110.46 102.04 110.46 104.72 108.57 105.44L57.78 124.7C57.27 124.89 56.72 124.89 56.21 124.7L5.42 105.44C3.53 104.72 3.53 102.04 5.42 101.32L56.21 82.06C56.72 81.87 57.27 81.87 57.78 82.06Z" fill="url(#paint_linear_54_273_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="多边形备份 3" d="M57.78 82.06L108.57 101.32C110.46 102.04 110.46 104.72 108.57 105.44L57.78 124.7C57.27 124.89 56.72 124.89 56.21 124.7L5.42 105.44C3.53 104.72 3.53 102.04 5.42 101.32L56.21 82.06C56.72 81.87 57.27 81.87 57.78 82.06ZM57.42 82.99L108.22 102.26C108.47 102.35 108.67 102.5 108.8 102.68C108.93 102.87 108.99 103.1 108.99 103.38C108.99 103.65 108.93 103.89 108.8 104.07C108.67 104.26 108.47 104.4 108.22 104.5L57.42 123.76C57.28 123.82 57.14 123.85 57 123.85C56.85 123.85 56.71 123.82 56.57 123.76L5.77 104.5C5.52 104.4 5.32 104.26 5.19 104.07C5.06 103.89 5 103.65 5 103.38C5 103.1 5.06 102.87 5.19 102.68C5.32 102.5 5.52 102.35 5.77 102.26L56.57 82.99C56.71 82.94 56.85 82.91 57 82.91C57.14 82.91 57.28 82.94 57.42 82.99Z" fill="url(#paint_linear_54_273_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="多边形备份 2" d="M57.78 69.84L108.57 89.1C110.46 89.82 110.46 92.5 108.57 93.22L57.78 112.48C57.27 112.67 56.72 112.67 56.21 112.48L5.42 93.22C3.53 92.5 3.53 89.82 5.42 89.1L56.21 69.84C56.72 69.65 57.27 69.65 57.78 69.84Z" fill="url(#paint_linear_54_274_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="多边形备份 2" d="M57.78 69.84L108.57 89.1C110.46 89.82 110.46 92.5 108.57 93.22L57.78 112.48C57.27 112.67 56.72 112.67 56.21 112.48L5.42 93.22C3.53 92.5 3.53 89.82 5.42 89.1L56.21 69.84C56.72 69.65 57.27 69.65 57.78 69.84ZM57.07 71.71L107.86 90.97C107.91 90.99 107.94 91.01 107.96 91.04C107.98 91.08 107.99 91.11 107.99 91.16C107.99 91.21 107.98 91.25 107.96 91.28C107.94 91.31 107.91 91.33 107.86 91.35L57.07 110.61C57.04 110.62 57.02 110.62 57 110.62C56.97 110.62 56.95 110.62 56.92 110.61L6.13 91.35C6.09 91.33 6.05 91.31 6.03 91.28C6.01 91.25 6 91.21 6 91.16C6 91.11 6.01 91.08 6.03 91.04C6.05 91.01 6.09 90.99 6.13 90.97L56.92 71.71C56.95 71.7 56.97 71.7 57 71.7C57.02 71.7 57.04 71.7 57.07 71.71Z" fill="url(#paint_linear_54_274_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="多边形" d="M57.78 58.56L108.57 77.83C110.46 78.54 110.46 81.22 108.57 81.94L57.78 101.2C57.27 101.39 56.72 101.39 56.21 101.2L5.42 81.94C3.53 81.22 3.53 78.54 5.42 77.83L56.21 58.56C56.72 58.37 57.27 58.37 57.78 58.56Z" fill="url(#paint_linear_54_275_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="多边形" d="M57.78 58.56L108.57 77.83C110.46 78.54 110.46 81.22 108.57 81.94L57.78 101.2C57.27 101.39 56.72 101.39 56.21 101.2L5.42 81.94C3.53 81.22 3.53 78.54 5.42 77.83L56.21 58.56C56.72 58.37 57.27 58.37 57.78 58.56ZM57.07 60.43L107.86 79.7C107.91 79.71 107.94 79.74 107.96 79.77C107.98 79.8 107.99 79.84 107.99 79.88C107.99 79.93 107.98 79.97 107.96 80C107.94 80.03 107.91 80.05 107.86 80.07L57.07 99.33C57.04 99.34 57.02 99.35 57 99.35C56.97 99.35 56.95 99.34 56.92 99.33L6.13 80.07C6.09 80.05 6.05 80.03 6.03 80C6.01 79.97 6 79.93 6 79.88C6 79.84 6.01 79.8 6.03 79.77C6.05 79.74 6.09 79.71 6.13 79.7L56.92 60.43C56.95 60.42 56.97 60.42 57 60.42C57.02 60.42 57.04 60.42 57.07 60.43Z" fill="url(#paint_linear_54_275_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<g filter="url(#filter_54_276_dd)">
		<ellipse id="椭圆形" cx="57.000000" cy="70.958740" rx="21.533333" ry="3.289474" fill="#000000" fill-opacity="0.800000"/>
		<ellipse id="椭圆形" cx="57.000000" cy="70.958740" rx="21.033333" ry="2.789474" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<path id="多边形" d="M56.82 0L98.79 18.42L98.79 66.91L57.42 91.16L16.04 66.91L16.04 18.42L56.82 0Z" fill="url(#paint_linear_54_277_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="多边形" d="M96.79 19.72L96.79 65.77L57.42 88.84L18.04 65.77L18.04 19.71L56.84 2.18L96.79 19.72ZM98.79 18.42L56.82 0L16.04 18.42L16.04 66.91L57.42 91.16L98.79 66.91L98.79 18.42Z" fill="url(#paint_linear_54_277_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="形状" d="" fill="url(#paint_linear_54_279_0)" fill-opacity="1.000000" fill-rule="nonzero"/>
	<path id="路径 copy" d="M53.42 20L67.35 20C67.74 20 67.97 20.69 67.75 21.21L62.75 33.06L71.51 33.06C71.92 33.06 72.14 33.81 71.89 34.32L56.33 65.7C56.01 66.34 55.37 65.86 55.48 65.05L58.01 45.53L44.49 45.81C44.11 45.82 43.87 45.16 44.06 44.64L53 20.38C53.08 20.14 53.24 20 53.42 20Z" fill="url(#paint_linear_54_271_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
</svg>
