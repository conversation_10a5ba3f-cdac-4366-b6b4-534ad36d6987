<svg width="112.000000" height="83.000000" viewBox="0 0 112 83" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_195_298_dd" x="0.000000" y="49.454529" width="86.000000" height="33.545471" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="4"/>
			<feGaussianBlur stdDeviation="3.33333"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<linearGradient id="paint_linear_195_297_0" x1="91.107841" y1="53.294651" x2="91.107841" y2="1.000000" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0077FF" stop-opacity="0.301961"/>
			<stop offset="0.572145" stop-color="#4593EC" stop-opacity="0.031373"/>
			<stop offset="1.000000" stop-color="#85BCFB" stop-opacity="0.000000"/>
		</linearGradient>
		<radialGradient id="paint_Diamond_195_298_0" cx="0.000000" cy="0.000000" r="1.000000" gradientUnits="userSpaceOnUse" gradientTransform="translate(43 66.2273) rotate(0) scale(43 110.239)">
			<stop stop-color="#0077FF"/>
			<stop stop-color="#0077FF" stop-opacity="0.027451"/>
			<stop offset="1.000000" stop-color="#66CFFF"/>
		</radialGradient>
		<pattern id="pattern_195_2900" patternContentUnits="objectBoundingBox" width="1.000000" height="1.000000">
			<use xlink:href="#image195_290_0" transform="matrix(0.007813,0,0,0.007813,0,0)"/>
		</pattern>
		<image id="image195_290_0" width="128.000000" height="128.000000" xlink:href="data:image/png;base64,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"/>
	</defs>
	<rect id="组件 10" width="112.000000" height="83.000000" fill="#FFFFFF" fill-opacity="0"/>
	<rect id="组件 6" width="86.000000" height="82.000000" transform="translate(0.000000 1.000000)" fill="#FFFFFF" fill-opacity="0"/>
	<path id="矩形" d="M0 1L86 1L86 66.2273C86 66.2273 82.9763 58.8283 80.8732 56.9091C78.7589 54.9797 75.5494 55.5786 73.5834 54.6978C64.0002 50.4045 48.9493 49.4545 43 49.4545C35.9084 49.4545 23.2086 49.4545 10.569 54.6978C8.9035 55.3887 4.33679 56.5626 2.37677 58.8109C0.765991 60.6586 0 66.2273 0 66.2273L0 1Z" fill="url(#paint_linear_195_297_0)" fill-opacity="1.000000" fill-rule="nonzero"/>
	<g filter="url(#filter_195_298_dd)">
		<ellipse id="椭圆 1" cx="43.000000" cy="66.227234" rx="43.000000" ry="16.772728" fill="url(#paint_Diamond_195_298_0)" fill-opacity="1.000000"/>
	</g>
	<path id="文本" d="" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
	<g opacity="0.800000">
		<path id="文本" d="" fill="#FFFFFF" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<rect id="用电量" x="6.999969" width="70.000000" height="70.000000" fill="url(#pattern_195_2900)" fill-opacity="1.000000"/>
</svg>
