import px from '@/utils/px';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';

const MultibarChart = ({ dataSeries, xAxisData, yAxisName, legendData }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (chartRef.current && !chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    const option = {
      grid: {
        top: '30%',
        right: '10%',
        bottom: '10%',
        left: '5%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        textStyle: {
          fontSize: px(10),
        },
      },
      legend: {
        left: 'center', // 图例居中
        top: '5%',
        orient: 'horizontal',
        data: legendData,
        textStyle: {
          color: '#fff',
          fontSize: px(14),
        },
        itemWidth: px(12),
        itemHeight: px(12),
      },
      xAxis: [
        {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            color: 'rgba(216, 240, 255, 0.8)',
            fontSize: px(12),
          },
          axisLine: {
            show: false,
          },
          position: 'bottom',
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: yAxisName,
          nameTextStyle: {
            color: 'rgba(216, 240, 255, 0.8)',
            fontSize: px(12),
          },
          axisLabel: {
            color: 'rgba(216, 240, 255, 0.8)',
            fontSize: px(12),
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            type: 'dashed',
            lineStyle: {
              type: 'dashed',

              width: 1, // 增加轴线宽度
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(200, 200, 200, 0)' },
                { offset: 0.7, color: 'rgb(48, 99, 180)' },
                { offset: 1, color: 'rgba(151, 151, 151, 0)' },
              ]),
            },
          },
        },
      ],
      series: dataSeries.map((data, index) => ({
        name: legendData[index],
        type: 'bar',
        barWidth: px(25),
        barGap: '10%',
        data: data,
        itemStyle: {
          borderRadius: 18,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color:
                index === 0
                  ? 'rgb(0, 119, 255)'
                  : `rgb(${Math.max(0, 0 - index * 20)}, ${119 + index * 20}, ${
                      255 - index * 15
                    })`,
            },
            {
              offset: 1,
              color:
                index === 0
                  ? 'rgba(0, 119, 255, 0.1)'
                  : `rgba(${Math.max(0, 0 - index * 20)}, ${
                      119 + index * 20
                    }, ${255 - index * 15}, 0.1)`,
            },
          ]),
        },
        label: {
          show: false,
          position: 'top',
          color: 'rgba(216, 240, 255, 0.8)',
        },
      })),
    };

    chartInstance.current.setOption(option);

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [dataSeries, xAxisData, yAxisName, legendData]);

  return <div ref={chartRef} style={{ height: '100%', width: '100%' }} />;
};

export default MultibarChart;
