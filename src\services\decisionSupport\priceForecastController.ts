// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';
import axios from 'axios';
const api = '/priceForecast';
const api1 = '/decisionSupport';
/** 2R机组电价 */
export async function AGetRPrice(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: {
    startDayStr: string;
    endDayStr: string;
  },
) {
  return request<API.R>(
    api + '/assistDecision/secondRPriceController/threePriceCurves',
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 2F机组电价 */
export async function AGetFPrice(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: {
    startDateStr: string;
    endDateStr: string;
  },
) {
  return request<API.R>(api1 + '/price2f/allPrice', {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

export const exportPrice = () => {
  axios
    .get(api + '/assistDecision/secondRPriceController/exportExcel', {
      responseType: 'blob',
    })
    .then((res) => {
      const data = res.data;
      const fileName = '虚拟电厂电价表';
      const blob = new Blob([data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      if (window.navigator && (window.navigator as any).msSaveOrOpenBlob) {
        (window.navigator as any).msSaveBlob(blob, fileName);
      } else {
        let u = window.URL.createObjectURL(blob);
        let aLink = document.createElement('a');
        aLink.style.display = 'none';
        aLink.href = u;
        aLink.setAttribute('download', fileName + '.xlsx');
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink);
        window.URL.revokeObjectURL(u);
      }
    })
    .catch((error) => {
      console.error('Export failed:', error);
      message.error('导出失败，请重试');
    });
};
