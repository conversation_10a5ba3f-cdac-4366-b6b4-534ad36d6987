<svg width="535.328613" height="1564.000000" viewBox="0 0 535.329 1564" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_43_4784_dd" x="15.708008" y="484.994629" width="4.984863" height="108.133301" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="1" dy="-1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.32 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4785_dd" x="15.708008" y="967.988770" width="4.984863" height="108.133301" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="1" dy="-1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.32 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4787_dd" x="1.000000" y="604.000000" width="76.000000" height="355.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="3" dy="1"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.1451 0 0 0 0 0.32157 0 0 0 0 0.46667 0 0 0 0.61 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-2"/>
			<feGaussianBlur stdDeviation="1"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.31 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_2"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="1"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.23137 0 0 0 0 1 0 0 0 0 0.95686 0 0 0 0.61 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_2" result="effect_innerShadow_3"/>
		</filter>
		<filter id="filter_43_4788_dd" x="16.000000" y="626.000000" width="35.000000" height="309.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="2" dy="-1"/>
			<feGaussianBlur stdDeviation="1.33333"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.57 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4789_dd" x="25.677734" y="652.240234" width="3.738525" height="256.635742" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="1" dy="-1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.32 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4793_dd" x="27.815186" y="55.979004" width="40.015137" height="93.772949" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="1" dy="-1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.32 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4790_dd" x="27.815186" y="55.979004" width="40.015137" height="93.772949" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4791_dd" x="27.831055" y="55.979004" width="24.493896" height="56.287109" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4792_dd" x="27.820801" y="55.979004" width="10.779541" height="25.980957" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4794_dd" x="27.546875" y="1465.401367" width="23.677734" height="54.787598" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="1" dy="-1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.32 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4796_dd" x="26.000000" y="652.000000" width="13.000000" height="257.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4800_dd" x="28.000000" y="32.000000" width="71.026855" height="117.714844" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4797_dd" x="28.000000" y="32.000000" width="71.026855" height="117.714844" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4798_dd" x="28.028076" y="32.000000" width="43.476807" height="70.658203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4799_dd" x="28.010010" y="32.000000" width="19.133789" height="32.614258" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4801_dd" x="28.000000" y="1465.000000" width="71.000000" height="71.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4802_dd" x="204.500000" y="77.500000" width="155.500000" height="89.500000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<linearGradient x1="236.139648" y1="257.500092" x2="461.000000" y2="257.500092" id="paint_linear_43_4744_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0B447C"/>
			<stop offset="1.000000" stop-color="#3397F9"/>
		</linearGradient>
		<linearGradient x1="383.768524" y1="341.105469" x2="383.768524" y2="1311.030396" id="paint_linear_43_4745_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0B3C70"/>
			<stop offset="0.492269" stop-color="#329BF9"/>
			<stop offset="1.000000" stop-color="#0B3D72"/>
		</linearGradient>
		<linearGradient x1="366.914520" y1="341.105469" x2="366.914520" y2="1311.030396" id="paint_linear_43_4746_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#040E2B"/>
			<stop offset="0.492269" stop-color="#459AED"/>
			<stop offset="1.000000" stop-color="#040E2B"/>
		</linearGradient>
		<linearGradient x1="403.800446" y1="368.547302" x2="403.800446" y2="1298.163330" id="paint_linear_43_4780_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0B457E"/>
			<stop offset="0.538071" stop-color="#299FFF"/>
			<stop offset="1.000000" stop-color="#0B447C"/>
		</linearGradient>
		<linearGradient x1="106.633957" y1="2.000000" x2="106.633957" y2="1562.000000" id="paint_linear_43_4783_0" gradientUnits="userSpaceOnUse">
			<stop offset="0.000929" stop-color="#0A447B"/>
			<stop offset="0.520487" stop-color="#0B457E"/>
			<stop offset="0.992366" stop-color="#0B447C"/>
		</linearGradient>
		<linearGradient x1="15.457352" y1="490.684174" x2="19.220039" y2="596.789856" id="paint_linear_43_4784_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#29384A"/>
			<stop offset="1.000000" stop-color="#1B2432"/>
		</linearGradient>
		<linearGradient x1="15.457336" y1="1070.432373" x2="19.220045" y2="964.326599" id="paint_linear_43_4785_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#29384A"/>
			<stop offset="1.000000" stop-color="#1B2432"/>
		</linearGradient>
		<linearGradient x1="367.032532" y1="2.000000" x2="481.558777" y2="51.792633" id="paint_linear_43_4786_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FF2B2B" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#7EFFEF" stop-opacity="0.501961"/>
		</linearGradient>
		<linearGradient x1="8.982449" y1="608.999939" x2="38.973621" y2="950.491455" id="paint_linear_43_4787_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#040E2B"/>
			<stop offset="0.492269" stop-color="#040E2B"/>
			<stop offset="1.000000" stop-color="#040E2B"/>
		</linearGradient>
		<linearGradient x1="25.971924" y1="626.000000" x2="25.971924" y2="935.000000" id="paint_linear_43_4788_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0E434C"/>
			<stop offset="0.512839" stop-color="#5597B2"/>
			<stop offset="1.000000" stop-color="#0D3844"/>
		</linearGradient>
		<linearGradient x1="25.489107" y1="665.743469" x2="28.311993" y2="917.567688" id="paint_linear_43_4789_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#29384A"/>
			<stop offset="1.000000" stop-color="#1B2432"/>
		</linearGradient>
		<linearGradient x1="25.803738" y1="60.912983" x2="56.007751" y2="152.927856" id="paint_linear_43_4793_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#29384A"/>
			<stop offset="1.000000" stop-color="#1B2432"/>
		</linearGradient>
		<linearGradient x1="26.356674" y1="1468.284058" x2="44.228870" y2="1522.044312" id="paint_linear_43_4794_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#29384A"/>
			<stop offset="1.000000" stop-color="#1B2432"/>
		</linearGradient>
	</defs>
	<path id="路径" d="M280 257.5C280 259.72 281.73 261.47 283.94 261.5L267.51 1410.1L267.51 1407.52L267.5 1407.5C265.26 1407.5 263.5 1409.26 263.5 1411.5C263.5 1413.74 265.26 1415.5 267.5 1415.5L267.51 1415.48L267.51 1411.5L401.5 1411.5L461 1308.06C461 1308.06 383.56 1075.62 383.56 815.97C383.56 556.3 461 344.73 461 344.73L417.5 257.5L284.01 257.5L284.01 253.52L284 253.5C281.76 253.5 280 255.26 280 257.5Z" fill="#D8D8D8" fill-opacity="0" fill-rule="evenodd"/>
	<path id="路径" d="M284 257.5L417.5 257.5L461 344.73C461 344.73 383.56 556.3 383.56 815.97C383.56 1075.62 461 1308.06 461 1308.06L401.5 1411.5L267.5 1411.5" stroke="url(#paint_linear_43_4744_0)" stroke-opacity="1.000000" stroke-width="8.000000" stroke-linejoin="round" stroke-linecap="round" stroke-dasharray="0 0"/>
	<g opacity="0.800000">
		<path id="路径" d="M452.67 341.1C400.81 464.29 370.72 632.47 370.72 818.01C370.72 1011.8 397.36 1149.64 448.43 1311.03L452.67 341.1Z" fill="#D8D8D8" fill-opacity="0" fill-rule="evenodd"/>
		<path id="路径" d="M452.67 341.1C400.81 464.29 370.72 632.47 370.72 818.01C370.72 1011.8 397.36 1149.64 448.43 1311.03" stroke="url(#paint_linear_43_4745_0)" stroke-opacity="1.000000" stroke-width="4.000000" stroke-dasharray="12 4"/>
	</g>
	<path id="路径" d="M435.82 341.1C383.95 464.29 353.87 632.47 353.87 818.01C353.87 1011.8 380.51 1149.64 431.58 1311.03L435.82 341.1Z" fill="#D8D8D8" fill-opacity="0" fill-rule="evenodd"/>
	<path id="路径" d="M435.82 341.1C383.95 464.29 353.87 632.47 353.87 818.01C353.87 1011.8 380.51 1149.64 431.58 1311.03" stroke="url(#paint_linear_43_4746_0)" stroke-opacity="1.000000" stroke-width="5.000000" stroke-dasharray="0 0"/>
	<g opacity="0.800000">
		<path id="形状结合" d="M441.502 1111.93C449.15 1145.06 458.103 1176.9 468.286 1207.16L455.545 1230.26C444.66 1197.94 435.099 1163.89 426.945 1128.42L441.502 1111.93ZM419.9 988.986C424.438 1025.49 430.425 1061.01 437.783 1095.24L423.233 1111.72C415.375 1075.15 408.99 1037.17 404.167 998.12L419.9 988.986ZM410.182 862.047C411.306 898.99 413.86 935.357 417.775 970.866L402.049 979.995C397.881 942.179 395.167 903.433 393.984 864.066L410.182 862.047ZM412.351 729.077C410.479 758.542 409.524 788.461 409.524 818.687C409.524 826.916 409.595 835.123 409.735 843.304L393.539 845.323C393.383 836.472 393.305 827.592 393.305 818.687C393.305 786.49 394.327 754.628 396.329 723.261L412.351 729.077ZM426.162 604.268C420.643 638.758 416.449 674.28 413.653 710.549L397.63 704.732C400.592 666.073 405.051 628.225 410.928 591.497L426.162 604.268ZM451.544 485.405C442.727 517.848 435.218 551.735 429.093 586.76L413.852 573.982C420.374 536.505 428.388 500.269 437.809 465.61L451.544 485.405ZM486.549 380.863C475.173 408.856 464.953 438.547 455.96 469.654L442.221 449.853C451.818 416.556 462.738 384.807 474.903 354.917L486.549 380.863ZM460.615 1244.91L473.357 1221.81C479.027 1237.74 485.047 1253.2 491.405 1268.15L493 1271.88L481.485 1298.16C474.101 1281.05 467.139 1263.28 460.615 1244.91Z" clip-rule="evenodd" fill="url(#paint_linear_43_4780_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="矩形" d="M529 46.5L389 46.5L352 188.5L219.5 188.5L172.5 97L92.33 97L56.08 188.5L27.53 250L27.53 298.5L27.53 461.5L27.53 494.33L56.08 585.5L92.33 671L92.33 925.5L56.08 989L56.08 1080L56.08 1109.5L56.08 1253L27.53 1288.5L27.53 1376L149.5 1500L468 1562L421.5 1562L149.5 1562L2 1562L2 2L465.62 2L529 46.5Z" fill="url(#paint_linear_43_4783_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="矩形" d="M389 46.5L352 188.5L219.5 188.5L172.5 97L92.33 97L56.08 188.5L27.53 250L27.53 298.5L27.53 461.5L27.53 494.33L56.08 585.5L92.33 671L92.33 925.5L56.08 989L56.08 1080L56.08 1109.5L56.08 1253L27.53 1288.5L27.53 1376L149.5 1500L468 1562L421.5 1562L149.5 1562L2 1562L2 2L465.62 2L529 46.5L389 46.5Z" stroke="#2194F2" stroke-opacity="1.000000" stroke-width="4.000000"/>
	<g opacity="0.600000" filter="url(#filter_43_4784_dd)">
		<path id="矩形" d="M15.7 484.99L20.69 496.53L20.69 593.12L15.7 593.12L15.7 484.99Z" fill="url(#paint_linear_43_4784_0)" fill-opacity="0.500000" fill-rule="evenodd"/>
	</g>
	<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g opacity="0.600000" filter="url(#filter_43_4785_dd)">
		<path id="矩形" d="M15.7 1076.12L20.69 1064.57L20.69 967.98L15.7 967.98L15.7 1076.12Z" fill="url(#paint_linear_43_4785_0)" fill-opacity="0.500000" fill-rule="evenodd"/>
	</g>
	<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g opacity="0.600000" filter="url(#filter_43_4787_dd)">
		<path id="矩形" d="M4 609L38.53 609L68 654.85L68 907.58L38.53 952L4 952L4 609Z" fill="url(#paint_linear_43_4787_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4788_dd)">
		<path id="矩形" d="M16 626L28.71 626L51 656.52L51 905.89L28.71 935L16 935L16 626Z" fill="url(#paint_linear_43_4788_0)" fill-opacity="0.500000" fill-rule="evenodd"/>
	</g>
	<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g opacity="0.600000" filter="url(#filter_43_4789_dd)">
		<path id="左侧灯光-暗" d="M25.67 652.24L29.41 660.88L29.41 900.22L25.67 908.87L25.67 652.24Z" fill="url(#paint_linear_43_4789_0)" fill-opacity="0.500000" fill-rule="evenodd"/>
	</g>
	<path id="左侧灯光-暗" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g opacity="0.600000" filter="url(#filter_43_4793_dd)">
		<mask id="mask_43_4793" fill="white">
			<path id="左上角-暗" d="M27.8311 102.087L27.8311 112.266L52.325 55.979L48.3933 55.979L27.8311 102.087ZM27.8208 81.96L27.8208 71.8384L34.6748 55.979L38.6003 55.979L27.8208 81.96ZM27.8152 139.616L27.834 149.752L67.8303 55.979L63.9548 55.979L27.8152 139.616Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
		</mask>
		<path id="左上角-暗" d="M27.8311 102.087L27.8311 112.266L52.325 55.979L48.3933 55.979L27.8311 102.087ZM27.8208 81.96L27.8208 71.8384L34.6748 55.979L38.6003 55.979L27.8208 81.96ZM27.8152 139.616L27.834 149.752L67.8303 55.979L63.9548 55.979L27.8152 139.616Z" clip-rule="evenodd" fill="url(#paint_linear_43_4793_0)" fill-opacity="0.500000" fill-rule="evenodd" mask="url(#mask_43_4793)"/>
	</g>
	<path id="左上角-暗" d="" clip-rule="evenodd" fill="#979797" fill-opacity="0.000000" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4794_dd)">
		<path id="左侧三角-暗" d="M27.54 1465.4L51.22 1520.19L27.54 1520.19L27.54 1465.4Z" fill="url(#paint_linear_43_4794_0)" fill-opacity="0.500000" fill-rule="evenodd"/>
	</g>
	<path id="左侧三角-暗" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4796_dd)">
		<path id="左侧灯光-亮" d="M26 652L39 660.66L39 900.33L26 909L26 652Z" fill="#299FFF" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="左侧灯光-亮" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4800_dd)">
		<mask id="mask_43_4800" fill="white">
			<path id="左上角-亮" d="M28.01 64.6143L28.01 51.9087L40.176 32L47.1438 32L28.01 64.6143ZM28.0281 89.8804L28.0281 102.658L71.5049 32L64.5261 32L28.0281 89.8804ZM28 136.992L28.0332 149.715L99.0269 32L92.1479 32L28 136.992Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
		</mask>
		<path id="左上角-亮" d="M28.01 64.6143L28.01 51.9087L40.176 32L47.1438 32L28.01 64.6143ZM28.0281 89.8804L28.0281 102.658L71.5049 32L64.5261 32L28.0281 89.8804ZM28 136.992L28.0332 149.715L99.0269 32L92.1479 32L28 136.992Z" clip-rule="evenodd" fill="#2194F2" fill-opacity="1.000000" fill-rule="evenodd" mask="url(#mask_43_4800)"/>
	</g>
	<path id="左上角-亮" d="" clip-rule="evenodd" fill="#979797" fill-opacity="0.000000" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4801_dd)">
		<path id="左侧三角-亮" d="M28 1465L99 1536L28 1536L28 1465Z" fill="#2194F2" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="左侧三角-亮" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4802_dd)">
		<path id="左侧三角-亮" d="M204.5 77.5L360 77.5L325.5 167L242.5 167L204.5 77.5Z" fill="#2194F2" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="左侧三角-亮" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
</svg>
