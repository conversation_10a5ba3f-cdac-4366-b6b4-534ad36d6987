import px from '@/utils/px';
import { Tooltip } from 'antd';

export const longRender = (value: any | { width: number }) => {
  if (value?.width) {
    return (cv: any) => {
      return (
        <Tooltip title={cv}>
          <div
            style={{
              width: value.width,
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              textAlign: 'center',
              whiteSpace: 'nowrap',
            }}
          >
            {cv}
          </div>
        </Tooltip>
      );
    };
  }
  return (
    <Tooltip title={value}>
      <div
        style={{
          maxWidth: px(180),
          textOverflow: 'ellipsis',
          overflow: 'hidden',
          textAlign: 'center',
          whiteSpace: 'nowrap',
        }}
      >
        {value}
      </div>
    </Tooltip>
  );
};
