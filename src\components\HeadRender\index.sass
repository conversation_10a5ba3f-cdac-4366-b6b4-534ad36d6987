@import '~@/assets/css/helpers.sass'
@font-face
    font-family: 庞门正道标题体
    src: url('../../assets/font/PangMenZhengDaoBiaoTiTiMianFeiBan-2.ttf')
@font-face
    font-family: youshe
    src: url('../../assets/font/YouSheBiaoTiHei-2.ttf') format('truetype')
@font-face
    font-family: shuhei
    src: url('/font/shuhei/AlimamaShuHeiTi-Bold.ttf')
@font-face
    font-family: zhanku
    src: url('/font/TsangerYuYangT_W04_W04.ttf')
@keyframes flash
    0%, 100%
        opacity: 1
    50%
        opacity: 0.5
.head
    background: $bg-color
    color: white
    height: px(70)
    width: 100%
    display: flex
    flex-direction: row
    align-items: center
    padding: 0 px(20)
    position: relative
.logo
    width: px(80)
    display: flex
    flex-direction: column
    align-items: center
    line-height: px(20)
    font-size: px(16)
    font-weight: bold
    justify-content: center
    img
        height: px(40)
.title
    width: 39%
    background-image: url(/headTitle1.png)
    background-size: 100% 100%
    background-repeat: no-repeat
    margin: 0 auto
    height: px(85)
    // width: 60%
    // cursor: pointer
    text-align: center
    padding-left: px(10)
    font-family: 庞门正道标题体
    font-size: px(38)
    // background-image: url(/titlebg.png)
    // background-size: 100% 100%
    // background-repeat: no-repeat
    letter-spacing: px(8)
    padding-top: px(5)
.leftBox,.rightBox
    width: calc(30% - px(200))
    display: flex
    flex-direction: row
    transition: all 0.3s ease
    &:hover
        .menuRight, .menuLeft
            opacity: 1
            transform: translateY(0)
.rightBox
    transform: translate(px(-45),0)
.leftBox
    transform: translate(px(45),0)
.time
    width: px(160)
    margin-right: px(-40)
    height: 100%
    display: flex
    flex-direction: column
    justify-content: center
    font-family: '庞门正道标题体'
    margin-left: px(5)
.year
    font-size: px(18)
    color: white
    line-height: px(20)
    letter-spacing: px(2)
.date
    font-size: px(16)
    color: #50a2ff
    line-height: px(30)
    margin-top: px(5)
    margin-left: px(5)
    letter-spacing: px(5)
.tool
    width: px(240)
    margin-left: px(-20)
    font-size: px(20)
    line-height: px(24)
    display: flex
    flex-direction: row
    align-items: center
    img
        width: px(40)
        height: px(40)
        margin-left: px(5)
.weatherIcon
    width: px(40)
    height: px(25) !important
    margin-right: px(15)
    margin-left: px(15)
.newMessage
    font-size: px(18)
    margin-bottom: px(20)
.newMessageTime
    font-size: px(20)
    color: #1296db
    font-weight: bold
    margin-bottom: px(10)
    img
        width: px(20)
        height: px(20)
        margin-right: px(10)
.user
    display: flex
    align-items: center
    margin-left: auto
    position: relative
    font-family: 庞门正道标题体
    font-size: px(16)
    cursor: pointer
    mr
.userIcon
    width: px(40)
    height: px(40)
    margin-left: px(5)
    cursor: pointer
    margin-right: px(5)
.flashImg
    animation: flash 1s infinite
.userName
    font-size: px(16)
    width: px(60)
    white-space: nowrap
    overflow: hidden
    text-overflow: ellipsis
.loginPop
    position: absolute
    top: px(50)
    right: 0
    background: rgba(6, 48, 109, 1)
    border-radius: px(10)
    padding: px(10) 0
    width: px(130)
    cursor: default
    display: flex
    flex-direction: column
    justify-content: center
    align-items: center
    &::before
        content: ""
        position: absolute
        top: px(-8)
        left: 50%
        transform: translateX(-50%)
        border-left: px(10) solid transparent
        border-right: px(10) solid transparent
        border-bottom: px(10) solid rgba(6, 48, 109, 1)
    .userItem
        cursor: pointer
        width: 100%
        padding: px(5) 0
        text-align: left
        &:hover
            color: #20dbfd
        .iconimg
            margin: 0 px(10) 0 px(20)
            width: px(20)
            height: px(20)
.longHead
    height: 100%
    width: 100%
    background-image: url(/headbg.png)
    background-size: 128% 100%
    background-repeat: no-repeat
    background-position: center
    position: relative
    font-family: 庞门正道标题体
.longHeadTitle
    width: 100%
    height: px(85)
    text-align: center
    padding-left: px(15)
    font-size: px(45)
    letter-spacing: px(10)
.longLogo
    position: absolute
    left: px(10)
    bottom: px(0)
    font-size: px(22)
    img
        height: px(50)
.longTime
    position: absolute
    bottom: px(-25)
    font-size: px(18)
    left: px(160)
.longTool
    position: absolute
    bottom: px(-2)
    right: px(-10)
    font-size: px(18)
    width: px(390)
    display: flex
    flex-direction: row
    align-items: center
    justify-content: space-between
    line-height: px(24)
.modal
    width: 100vw
    height: 100vh
    background-color: black
    z-index: 100000 !important
    position: fixed
.menu
    &Right, &Left
        background-image: url(@/assets/header/tab_right.png)
        background-size: 100% 100%
        font-size: px(20)
        font-family: 庞门正道标题体
        color: rgb(139, 180, 226)
        width: px(260)
        height: px(40)
        line-height: px(40)
        text-align: center
        cursor: pointer
        background-repeat: no-repeat
        transition: all 0.3s ease
        opacity: 0
        transform: translateY(px(10))
        &:hover
            color: rgb(220, 234, 249)
            text-shadow: 0 0 px(2) rgb(177, 212, 251)
            transform: translateY(px(-2))
            background-position: 0 px(3), 0 0

    &Right
        margin-right: px(-30)
        clip-path: polygon(22% 0, 100% 0, 77% 100%, 0 100%)
        &:hover
            background-image: url(@/assets/header/light.png),url(@/assets/header/tab_right.png)
    &Left
        background-image: url(@/assets/header/tab_left.png)
        margin-left: px(-30)
        clip-path: polygon(0 0, 77% 0, 100% 100%, 23% 100%)
        &:hover
            background-image: url(@/assets/header/light.png),url(@/assets/header/tab_left.png)
    &RightActive, &LeftActive
        color: rgb(220, 234, 249)
        text-shadow: 0 0 px(2) rgb(177, 212, 251)
        transform: translateY(0)
        background-position: 0 px(3), 0 0
    &RightActive
        background-image: url(@/assets/header/light.png),url(@/assets/header/tab_right.png)
    &LeftActive
        background-image: url(@/assets/header/light.png),url(@/assets/header/tab_left.png)

div .menuLeft___Xioch:nth-child(3)
        position: relative
