@font-face
  font-family: youshe
  src: url('../../assets//font/YouSheBiaoTiHei-2.ttf') format('truetype')

@import '~@/assets/css/helpers.sass'

.box
  width: fit-content
  height: fit-content
  display: flex
  flex-direction: column
  color: white
  :global
    .ant-picker
      width: px(230)
      padding: px(3) px(10) !important
      background: rgba(0,0,0,0)
      border: white solid px(1)
      line-height: 0
      height: px(34) !important
    .ant-picker-input >input,.ant-picker-separator,.ant-picker-suffix,
    .ant-picker .ant-picker-input >input::placeholder,
    .ant-select-single .ant-select-selector,
    .ant-select .ant-select-arrow,
    .ant-select-selection-item
      color: white !important
      font-size: px(14)
    .ant-select-selector,.ant-select-single .ant-select-selector
      background: rgba(0,0,0,0)
      border: white solid px(1)
    .ant-select-selector
      padding: px(3) px(10) !important
      // line-height: px(14) !important
      height: px(34) !important
    .ant-select-selection-item,.ant-select-selection-placeholder
      font-size: px(14)
      line-height: px(24) !important
    .ant-select-arrow
      margin-top: px(12)
      top: 0

.title
  padding: px(10) px(15)
  padding-right: 0
  background-image: url('../../assets/bg//markettrading/tab_bg.png')
  background-size: 100% 100%
  font-family: youshe
  letter-spacing: px(5)
  font-weight: 10 !important
  font-size: px(24)
  // text-shadow: 0 0 px(5) rgb(5, 88, 243), px(2) px(2) px(5) rgb(5, 88, 243)
.options
  display: flex
  flex-direction: row
  width: 100%
  height: fit-content
  margin-top: px(5)
.select
  margin-right: px(10)
  min-width: px(100)
.rangeDate
  width: px(220) !important
.search
  border: solid white 1px
  border-radius: px(5)
  display: flex
  align-items: center
  justify-content: center
  height: px(32)
  width: px(32)
  align-self: center
  margin-left: px(10)
  font-size: px(18)
  cursor: pointer
  &:hover
    border-color: rgb(121, 162, 238)
    color: rgb(121, 162, 238)
.date
  width: px(150)
