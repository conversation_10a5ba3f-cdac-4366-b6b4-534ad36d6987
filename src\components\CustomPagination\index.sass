@import '@/assets/css/helpers.sass'
.box
    color: white
    :global
        .ant-pagination-jump-next,.ant-pagination-prev,.ant-pagination-next
            color: white
            font-size: px(16)
        .ant-pagination-next, .ant-pagination-prev,.ant-pagination-item,
        .ant-pagination-item-link,.ant-pagination-jump-next,.ant-pagination-jump-prev,.ant-pagination-item-active
            background-color: rgba(6, 48, 109, 0)
            background: rgba(6, 48, 109, 0)
            height: px(30)
            line-height: px(30)
        .ant-pagination-item:hover
            background: rgba(6, 48, 109, 0) !important
            border: px(2) solid #2080fd
            color: white
        .ant-pagination-mini .ant-pagination-prev,
        .ant-pagination-mini .ant-pagination-item,
        .ant-pagination-mini .ant-pagination-next,
        .ant-pagination-mini .ant-pagination-jump-prev,
        .ant-pagination-mini .ant-pagination-jump-next,
        .ant-pagination-mini .ant-pagination-item-active
            height: px(30)
            line-height: px(30)
            width: px(38)
            margin: px(2)
            color: white
        .ant-pagination-item a
            background: rgba(6, 48, 109, 0)
            border-radius: px(5)
            font-size: px(16)
            width: px(38)
            height: px(30)
            line-height: px(30)
            color: white
        .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis,
        .ant-pagination-disabled .ant-pagination-item-link,
        .ant-pagination-prev .ant-pagination-item-link,
        .ant-pagination-next .ant-pagination-item-link,
        .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis
            color: white
            font-size: px(16)
