// @ts-ignore
/* eslint-disable */
import { mock } from '@/utils/util';
import { message } from 'antd';
import axios from 'axios';
const api = '/decisionSupport';

// 创建一个新的 axios 实例，不继承全局配置
const customAxios = axios.create({
  baseURL: '/',
});

/* 获取直调负荷对比数据 */
export const getDirectlyElectricalLoad = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], direct: [], entire: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/disclosure/directlyElectricalLoad', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/** 获取风光对比数据*/
export const getNewEnergy = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], wind: [], pv: [], unit: '' } });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/disclosure/newEnergy', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取价格对比数据 */
export const getElectricityPrice = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], downPowerList: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/disclosure/electricityPrice', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取联络线负荷对比数据 */
export const getConnectLinesLoad = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], nonMarketList: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/disclosure/connectLinesLoad', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/** 获取抽蓄对比数据*/
export const getPumpedStorage = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], pumpedList: [], unit: '' } });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/disclosure/pumpedStorage', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

// ==================== 连续日期接口 (V2) ====================

/* 获取直调负荷对比数据 - 连续日期版本 */
export const getDirectlyElectricalLoadV2 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: { dateList: [], timeList: [], direct: [], entire: [], unit: '' },
      });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/disclosureV2/directlyElectricalLoad', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/** 获取风光对比数据 - 连续日期版本 */
export const getNewEnergyV2 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: { dateList: [], timeList: [], wind: [], pv: [], unit: '' },
      });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/disclosureV2/newEnergy', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取价格对比数据 - 连续日期版本 */
export const getElectricityPriceV2 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: { dateList: [], timeList: [], downPowerList: [], unit: '' },
      });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/disclosureV2/electricityPrice', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取联络线负荷对比数据 - 连续日期版本 */
export const getConnectLinesLoadV2 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: { dateList: [], timeList: [], nonMarketList: [], unit: '' },
      });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/disclosureV2/connectLinesLoad', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/** 获取抽蓄对比数据 - 连续日期版本 */
export const getPumpedStorageV2 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: { dateList: [], timeList: [], pumpedList: [], unit: '' },
      });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/disclosureV2/pumpedStorage', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

// ==================== 原有单日期接口 ====================

export const exportDisclosureComparison = (params: { dateStr: string }) => {
  axios
    .get(api + '/disclosure/exportExcel', {
      responseType: 'blob',
      params,
    })
    .then((res) => {
      const data = res.data;
      const fileName = `预测与实际披露对比表_${params.dateStr}`;
      const blob = new Blob([data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      if (window.navigator && (window.navigator as any).msSaveOrOpenBlob) {
        (window.navigator as any).msSaveBlob(blob, fileName);
      } else {
        let u = window.URL.createObjectURL(blob);
        let aLink = document.createElement('a');
        aLink.style.display = 'none';
        aLink.href = u;
        aLink.setAttribute('download', fileName + '.xlsx');
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink);
        window.URL.revokeObjectURL(u);
      }
    })
    .catch((error) => {
      console.error('Export failed:', error);
      message.error('导出失败，请重试');
    });
};
