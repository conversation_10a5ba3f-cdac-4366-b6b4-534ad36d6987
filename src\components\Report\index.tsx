import CustomButton from '@/components/CustomButton';
import CustomModal from '@/components/CustomModal';
import CustomTable from '@/components/CustomTable';
import { AEditJudgment, AGetReport } from '@/services/login';
import px from '@/utils/px';
import { useAntdTable } from 'ahooks';
import { Form } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useState } from 'react';
import CustomSelect from '../CustomSelect';
import styles from './index.sass';

interface IProps {
  open: boolean;
  onClose: () => void;
}

export default function Index(props: IProps) {
  const { open, onClose } = props;
  const [openEdit, setOpenEdit] = useState(false);
  const [current, setCurrent] = useState('');
  const {} = props;
  const [form] = Form.useForm();
  const [tipValue, setTipValue] = useState('安全');
  const getTableData = (
    params: { pageSize: number; current: number },
    formData: any,
  ) => {
    const { pageSize, current } = params;
    return AGetReport({
      logType: formData?.logType || '操作日志',
      pageSize,
      current,
    });
  };

  const { tableProps, search, refresh } = useAntdTable(getTableData, {
    form,
    defaultParams: [{ current: 1, pageSize: 8 }],
    defaultType: 'advance',
  });
  const { submit, reset } = search;
  const onEdit = () => {
    AEditJudgment({
      id: current,
      judgmentStr: tipValue,
    }).then(() => {
      setOpenEdit(false);
      refresh();
    });
  };
  const columns: ColumnsType<any> = [
    {
      title: '编号',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
    },
    {
      title: '日志类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '模块名称',
      dataIndex: 'module',
      key: 'module',
    },
    {
      title: '日志级别',
      dataIndex: 'level',
      key: 'level',
    },
    {
      title: '日志内容',
      dataIndex: 'content',
      key: 'content',
    },
    {
      title: '操作者',
      dataIndex: 'user',
      key: 'user',
    },
    {
      title: '审计标志',
      dataIndex: 'judgmentStr',
      key: 'judgmentStr',
      render: (value) => {
        let color = 'green';
        if (value === '安全') {
          color = '#42ff3c';
        } else if (value === '警示') {
          color = '#fdea43';
        } else {
          color = '#fd4343';
        }
        if (!value) return '';
        return (
          <div
            className={styles.tip}
            style={{
              color: color,
            }}
          >
            {value}
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'id',
      key: 'id',
      render: (value: string) => {
        return (
          <CustomButton
            onClick={() => {
              setOpenEdit(true);
              setCurrent(value);
            }}
          >
            编辑
          </CustomButton>
        );
      },
    },
  ];
  return (
    <CustomModal
      width={px(1200)}
      title="日志查看"
      onCancel={onClose}
      footer={[]}
      open={open}
      centered
    >
      <div className={styles.box}>
        <Form.Item name="logType" label="定期备份周期">
          <CustomSelect
            className={styles.select}
            defaultValue={'每天'}
            options={[
              {
                label: '每天',
                value: '每天',
              },
              {
                label: '每周',
                value: '每周',
              },
              {
                label: '每月',
                value: '每月',
              },
            ]}
          ></CustomSelect>
        </Form.Item>

        <Form className={styles.form} form={form}>
          <Form.Item name="logType" label="类型">
            <CustomSelect
              className={styles.select}
              defaultValue={'操作日志'}
              options={[
                {
                  label: '操作日志',
                  value: '操作日志',
                },
                {
                  label: '系统日志',
                  value: '系统日志',
                },
              ]}
            ></CustomSelect>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={submit}>
              筛选
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton onClick={reset}>重置</CustomButton>
          </Form.Item>
        </Form>
        <div className={styles.table}>
          <CustomTable columns={columns} {...tableProps}></CustomTable>
        </div>
      </div>
      <CustomModal
        title="日志查看"
        onCancel={onClose}
        footer={[]}
        open={openEdit}
        centered
      >
        <div className={styles.editBox}>
          设置审计标志：
          <CustomSelect
            value={tipValue}
            onChange={(value) => setTipValue(value)}
            className={styles.select}
            options={[
              {
                label: '安全',
                value: '安全',
              },
              {
                label: '警示',
                value: '警示',
              },
              {
                label: '异常',
                value: '异常',
              },
            ]}
          ></CustomSelect>
        </div>
        <div className={styles.buttons}>
          <CustomButton onClick={onEdit}>确定</CustomButton>
          <CustomButton onClick={() => setOpenEdit(false)}>取消</CustomButton>
        </div>
      </CustomModal>
    </CustomModal>
  );
}
