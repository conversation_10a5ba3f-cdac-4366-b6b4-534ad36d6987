import { ConfigProvider, Table, TableProps } from 'antd'; // 注意这里导入了TableProps类型和ConfigProvider
import React from 'react';
import styles from './index.sass';

const CustomTable: React.FC<TableProps<any>> = (props) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            stickyScrollBarBg: '#f0f0f0', // 设置为更明亮的颜色
          },
        },
      }}
    >
      <Table {...props} className={`${styles.table} ${props.className}`} />
    </ConfigProvider>
  );
};

export default CustomTable;
