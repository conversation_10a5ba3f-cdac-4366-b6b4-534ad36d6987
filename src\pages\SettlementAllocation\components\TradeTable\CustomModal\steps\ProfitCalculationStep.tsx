import px from '@/utils/px';
import { ConfigProvider, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React from 'react';
import styles from './index.sass';
interface ProfitCalculationStepProps {
  data: any[];
}

const ProfitCalculationStep: React.FC<ProfitCalculationStepProps> = ({
  data,
}) => {
  // 第三步：收益计算表格列配置
  const columns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'timeStr',
      key: 'timeStr',
      width: px(120),
      align: 'center',
      fixed: 'left',
    },
    {
      title: '整体调节量(MW)',
      dataIndex: 'wholeAdjustEnergy',
      key: 'wholeAdjustEnergy',
      width: px(120),
      align: 'center',
    },
    {
      title: '特来电调节量(MW)',
      dataIndex: 'teldAdjustEnergy',
      key: 'teldAdjustEnergy',
      width: px(120),
      align: 'center',
    },
    {
      title: '电表调节量(MW)',
      dataIndex: 'meterAdjustEnergy',
      key: 'meterAdjustEnergy',
      width: px(120),
      align: 'center',
    },
    {
      title: '中长期电价(元/MWh)',
      dataIndex: 'longPrice',
      key: 'longPrice',
      width: px(130),
      align: 'center',
    },
    {
      title: '实时电价(元/MWh)',
      dataIndex: 'normalRealTimePrice',
      key: 'normalRealTimePrice',
      width: px(120),
      align: 'center',
    },
    {
      title: '整体收益(元)',
      dataIndex: 'wholeProfit',
      key: 'wholeProfit',
      width: px(120),
      align: 'center',
    },
    {
      title: '特来电收益(元)',
      dataIndex: 'teldProfit',
      key: 'teldProfit',
      width: px(120),
      align: 'center',
    },
    {
      title: '电表收益(元)',
      dataIndex: 'meterProfit',
      key: 'meterProfit',
      width: px(120),
      align: 'center',
    },
    {
      title: 'VPP收益(元)',
      dataIndex: 'vppProfit',
      key: 'vppProfit',
      width: px(120),
      align: 'center',
    },
  ];

  return (
    <div className={styles.stepContent}>
      <h3 className={styles.stepTitle}>收益计算</h3>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              stickyScrollBarBg: '#f0f0f0', // 设置为更明亮的颜色
            },
          },
        }}
      >
        <Table
          dataSource={data}
          columns={columns}
          className={styles.table}
          size="small"
          pagination={false}
          scroll={{ x: px(1900), y: px(400) }}
        />
      </ConfigProvider>
    </div>
  );
};

export default ProfitCalculationStep;
