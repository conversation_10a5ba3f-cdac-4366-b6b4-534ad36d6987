import CustomButton from '@/components/CustomButton';
import { exportPrice } from '@/services/decisionSupport/priceForecastController';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import styles from './index.sass';
import RPriceLine from './RPriceLine';
const PricingStrategy: React.FC = () => {
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs(),
  ]);

  const handleDateRangeChange = (dates: any) => {
    if (dates) {
      setDateRange(dates);
    }
  };
  return (
    <div className={styles.container}>
      <div className={styles.dateRange}>
        <DatePicker.RangePicker
          allowClear={false}
          value={dateRange}
          onChange={handleDateRangeChange}
          format="YYYY-MM-DD"
          placeholder={['开始日期', '结束日期']}
        />
        <CustomButton
          type="primary"
          onClick={() =>
            exportPrice({
              startDate: dateRange[0].format('YYYY-MM-DD'),
              endDate: dateRange[1].format('YYYY-MM-DD'),
            })
          }
          className={styles.exportBtn}
        >
          导出
        </CustomButton>
      </div>
      <div className={styles.gridLayout}>
        <div className={styles.gridItem}>
          <RPriceLine dateRange={dateRange} />
        </div>
      </div>
    </div>
  );
};

export default PricingStrategy;
