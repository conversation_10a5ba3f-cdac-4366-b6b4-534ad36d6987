
@import '@/assets/css/helpers.sass'
.box

.header
    margin-bottom: px(20)
.form
    display: flex
    flex-direction: row
    align-items: center
    :global
        .ant-form-item .ant-form-item-label >label
            color: white
            font-size: px(18)
            height: px(40)
        button,input
            margin-left: px(20)
.input,.select
    width: px(200)
    min-width: px(150)
    margin-right: px(15)
.operate
    span
        margin: 0 px(10)
        cursor: pointer
        color: yellow
.select
    max-width: px(150)
.editBox
    display: flex
    justify-content: center
    margin-top: px(30)
    font-size: px(18)
.buttons
    display: flex
    justify-content: center
    margin-top: px(50)
    button
        margin: 0 px(20)
.tip
    font-weight: bold
    background: rgba(111, 111, 111, 0.1)
    padding: px(5) 0
    border-radius: px(10)
    border: 1px solid #757474
