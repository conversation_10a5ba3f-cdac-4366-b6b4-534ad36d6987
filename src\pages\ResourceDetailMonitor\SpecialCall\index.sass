@import '~@/assets/css/helpers.sass'

@font-face
    font-family: youshe
    src: url('../../../assets//font/YouSheBiaoTiHei-2.ttf') format('truetype')
$bdcolor: #4987D4
$bdwidth: px(4)
.page
    height: 100%
    padding-top: px(10)
    .top
        height: px(510)
        width: 100%
        padding: px(15) px(10)
        background: linear-gradient(to bottom,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left top,linear-gradient(to bottom,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right top,linear-gradient(to top,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right bottom,linear-gradient(to top,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left bottom,linear-gradient(to right,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left top,linear-gradient(to right,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left bottom,linear-gradient(to left,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right top,linear-gradient(to left,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right bottom,
        background-size: px(40) px(40)
        background-repeat: no-repeat
        background-color: rgba(13, 44, 91, 0.2)
        box-shadow: inset 0 0 px(4) 2px #2F60A1
    .bottom
        display: flex
        flex-direction: row
        justify-content: space-between
        margin-top: px(15)
    .bottomLeft,.bottomRight
        position: relative
        padding: px(10)
        width: px(930)
        height: px(360)
        background: linear-gradient(to bottom,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left top,linear-gradient(to bottom,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right top,linear-gradient(to top,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right bottom,linear-gradient(to top,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left bottom,linear-gradient(to right,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left top,linear-gradient(to right,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left bottom,linear-gradient(to left,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right top,linear-gradient(to left,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right bottom,
        background-size: px(40) px(40)
        background-repeat: no-repeat
        background-color: rgba(13, 44, 91, 0.2)
        box-shadow: inset 0 0 px(4) 2px #2F60A1
.filter
    display: flex
    justify-content: end
    margin: px(10)
.select
    min-width: px(200)
.table
    display: flex
    flex-direction: row
    flex-wrap: wrap
    justify-content: space-between
    padding-top: px(10)
    .card
        margin: 0 px(10) px(20) px(10)
    .cardTitle
        cursor: default
        width: px(440)
        height: px(35)
        line-height: px(35)
        padding-left: px(10)
        color: #f1f3f7
        font-weight: bold
        background-image: url('../../../assets/bg/cardTitle.png')
        background-size: 100% 100%
        font-size: px(18)

    .titleText
        width: px(380)
        overflow: hidden
        text-overflow: ellipsis
        white-space: nowrap
    .floor
        font-size: px(16)
        width: px(445)
        height: px(160)
        color: #B6FFFF
        display: flex
        align-items: center
        padding: px(20)
        border-radius: 0 0 px(20) px(20)
        background: #233C61
        .icon
            img
                margin-right: px(20)
                width: px(64)
                height: px(60)
    .view
        color: #09e5f5
        cursor: pointer
    .content
        width: px(400)
        display: flex
        flex-direction: column
        justify-content: space-between
    .power
        display: flex
    .label
        width: px(80)
        text-align: right
    .value
        color: white
        width: px(250)
.title
    position: absolute
    top: px(15)
    left: 50%
    font-size: px(18)
    transform: translate(-50%)
    font-weight: bold
    color: white
.total
    position: absolute
    top: px(10)
    left: 88%
    width: px(100)
    height: px(36)
    border: px(1) solid rgba(30, 123, 214, 0.8)
    margin-right: px(10)
    text-align: center
    line-height: px(33)
    color: rgba(216, 240, 255, 0.5)
    text-shadow: 0 0 5px rgb(0, 145, 255)
    cursor: pointer
    &.active,&:hover
        color: rgb(216, 240, 255)
        text-shadow: 0 0 10px rgb(0, 145, 255)
        border: px(1) solid rgba(30, 123, 214, 0.8)
        background: linear-gradient(to bottom, rgba(15, 74, 159, 0), rgba(17, 75, 160, 0.95))
.total
    position: absolute
    top: px(10)
    left: 88%
    width: px(100)
    height: px(36)
    border: px(1) solid rgba(30, 123, 214, 0.8)
    margin-right: px(10)
    text-align: center
    line-height: px(33)
    color: rgba(216, 240, 255, 0.5)
    text-shadow: 0 0 5px rgb(0, 145, 255)
    cursor: pointer
    &.active,&:hover
        color: rgb(216, 240, 255)
        text-shadow: 0 0 10px rgb(0, 145, 255)
        border: px(1) solid rgba(30, 123, 214, 0.8)
        background: linear-gradient(to bottom, rgba(15, 74, 159, 0), rgba(17, 75, 160, 0.95))
.total2
    position: absolute
    top: px(10)
    left: 73%
    width: px(120)
    height: px(36)
    border: px(1) solid rgba(30, 123, 214, 0.8)
    margin-right: px(10)
    text-align: center
    line-height: px(33)
    color: rgba(216, 240, 255, 0.5)
    text-shadow: 0 0 5px rgb(0, 145, 255)
    cursor: pointer
    &.active,&:hover
        color: rgb(216, 240, 255)
        text-shadow: 0 0 10px rgb(0, 145, 255)
        border: px(1) solid rgba(30, 123, 214, 0.8)
        background: linear-gradient(to bottom, rgba(15, 74, 159, 0), rgba(17, 75, 160, 0.95))
.pagination
    float: right
.none
    color: white
    display: flex
    flex-direction: column
    justify-content: center
    align-items: center
    width: 100%
    height: 100%
    font-size: px(18)
    padding-top: px(50)
    img
        margin-top: px(40)
        width: px(120)
        height: px(120)
