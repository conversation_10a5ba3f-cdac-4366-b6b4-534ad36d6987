import CustomButton from '@/components/CustomButton';
import CustomDate from '@/components/CustomDate';
import CustomTableV2 from '@/components/CustomTableV2';
import {
  ADownLoadExcel,
  AGetMedLongTable,
} from '@/services/settlementAllocation';
import px from '@/utils/px';
import { useAntdTable } from 'ahooks';
import { Form, message } from 'antd';
import Table, { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import { IResourceBenefitData } from '../../typing';
import styles from './index.sass';

interface Props {
  name?: string;
}
const Index: React.FC<Props> = (props: Props) => {
  const {} = props;
  const container = useRef<HTMLDivElement>(null);

  const [form] = Form.useForm();
  const getTableData = (
    params: { pageSize: number; current: number },
    formData?: any,
  ) => {
    const { pageSize, current } = params;
    const { time } = formData || {};
    return AGetMedLongTable({
      pageSize,
      current,
      dateStr: time.format('YYYY-MM-DD'),
    });
  };

  const { tableProps, search } = useAntdTable(getTableData, {
    form,
    defaultParams: [{ current: 1, pageSize: 20 }],
    defaultType: 'advance',
  });

  const { submit, reset } = search;

  const columns: ColumnsType<IResourceBenefitData> = [
    {
      title: '时刻',
      dataIndex: 'period',
      key: 'period',
      align: 'center',
    },
    {
      title: '总中长期',
      children: [
        {
          title: '净合约量(兆瓦时)',
          dataIndex: 'mlVol',
          key: 'mlVol',
          align: 'center',
          render: (v) => v || '-',
        },
        {
          title: '加权均价(元/兆瓦时)',
          dataIndex: 'mlPrice',
          key: 'mlPrice',
          align: 'center',
          render: (v) => v || '-',
        },
      ],
    },
    {
      title: '政府授权',
      children: [
        {
          title: '净合约量(兆瓦时)',
          dataIndex: 'gaVol',
          key: 'gaVol',
          align: 'center',
          render: (v) => v || '-',
        },
        {
          title: '加权均价(元/兆瓦时)',
          dataIndex: 'gaPrice',
          key: 'gaPrice',
          align: 'center',
          render: (v) => v || '-',
        },
      ],
    },
    {
      title: '计算剩余中长期',
      children: [
        {
          title: '净合约量(兆瓦时)',
          dataIndex: 'reVol',
          key: 'reVol',
          align: 'center',
          render: (v) => v || '-',
        },
        {
          title: '加权均价(元/兆瓦时)',
          dataIndex: 'rePrice',
          key: 'rePrice',
          align: 'center',
          render: (v) => v || '-',
        },
      ],
    },
  ];

  return (
    <>
      <div className={styles.box} ref={container}>
        <Form
          className={styles.form}
          form={form}
          initialValues={{
            time: dayjs('2025-07-14'),
          }}
        >
          <Form.Item name="time" label="选择日期">
            <CustomDate allowClear={false} className={styles.date}></CustomDate>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={submit}>
              筛选
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={reset}>
              重置
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton
              className={styles.button}
              onClick={() => {
                ADownLoadExcel({ type: '资源收益' }).then(() => {
                  message.success('下载成功！');
                });
              }}
            >
              下载
            </CustomButton>
          </Form.Item>
        </Form>
        <div className={styles.table}>
          <CustomTableV2
            {...tableProps}
            bordered
            columns={columns}
            scroll={{ y: px(570) }}
            pagination={false}
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>当日总计</Table.Summary.Cell>
                  <Table.Summary.Cell index={1}></Table.Summary.Cell>
                </Table.Summary.Row>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>当月累计</Table.Summary.Cell>
                  <Table.Summary.Cell index={1}></Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            )}
          ></CustomTableV2>
        </div>
      </div>
    </>
  );
};

export default Index;
