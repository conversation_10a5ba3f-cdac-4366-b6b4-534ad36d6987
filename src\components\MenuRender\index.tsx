import MenuGroup from '@/components/MenuGroup';
import React, { useEffect, useState } from 'react';
import { history } from 'umi';
import styles from './index.sass';

import buy from '@/assets/menu/buy.png';
import checkout from '@/assets/menu/checkout.png';
import decide from '@/assets/menu/decide.svg';
import efficient from '@/assets/menu/efficient.svg';
import management from '@/assets/menu/management.svg';
import monitor from '@/assets/menu/monitor.svg';

const MenuRender: React.FC<{ position: 'left' | 'right' }> = ({ position }) => {
  const [activeMenu, setActiveMenu] = useState<string | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest(`.${styles.menuWrapper}`)) {
        setActiveMenu(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const leftMenuData = [
    {
      key: '1',
      name: '首页',
      path: '/resource-monitor',
      icon: monitor,
    },
    {
      key: '2',
      name: '资源管理',
      path: '/resource-management',
      icon: management,
      children: [
        {
          key: '2-1',
          name: '基本信息',
          path: '/resource-management',
        },
        {
          key: '2-2',
          name: '数据监测',
          path: '/resource-detail-monitor',
        },
      ],
    },
    {
      key: '3',
      name: '辅助决策',
      path: '/decision-support',
      icon: decide,
      children: [
        {
          key: '3-1',
          name: '负荷预测',
          path: '/decision-support/load-forecasting',
        },
        {
          key: '3-2',
          name: '对比分析',
          path: '/decision-support/comparison-analysis',
        },
        {
          key: '3-3',
          name: '日报分析',
          path: '/decision-support/daily-report',
        },
        {
          key: '3-4',
          name: '电价分析',
          path: '/decision-support/pricing-strategy',
        },
        {
          key: '3-5',
          name: '调度策略',
          path: '/decision-support/scheduling-strategy',
        },
      ],
    },
  ];

  const rightMenuData = [
    {
      key: '4',
      name: '高效调控',
      path: '/efficient-control',
      icon: efficient,
    },
    {
      key: '5',
      name: '市场交易',
      path: '/market-trading',
      icon: buy,
      children: [
        {
          key: '5-1',
          name: '调节量交易',
          path: '/market-trading/market-trading',
        },
        {
          key: '5-2',
          name: '中长期市场',
          path: '/market-trading/long-trading',
        },
        {
          key: '5-3',
          name: '需求响应',
          path: '/market-trading/demand-response',
        },
        {
          key: '5-4',
          name: '辅助服务',
          path: '/market-trading/auxiliary-service',
        },
      ],
    },
    {
      key: '6',
      name: '结算分配',
      path: '/settlement-allocation',
      icon: checkout,
    },
  ];

  const menuData = position === 'left' ? leftMenuData : rightMenuData;

  const handleMenuClick = (e: React.MouseEvent, item: any) => {
    e.stopPropagation();
    if (item.children) {
      setActiveMenu(activeMenu === item.key ? null : item.key);
    } else {
      history.push(item.path);
      setActiveMenu(null);
    }
  };

  const handleSubMenuClick = (e: React.MouseEvent, path: string) => {
    e.stopPropagation();
    history.push(path);
    setActiveMenu(null);
  };

  return (
    <div className={position === 'left' ? styles.leftMenu : styles.rightMenu}>
      {menuData.map((item) => (
        <div key={item.key} className={styles.menuWrapper}>
          <div onClick={(e) => handleMenuClick(e, item)}>
            <MenuGroup title={item.name} icon={item.icon} />
          </div>
          {item.children && activeMenu === item.key && (
            <div className={styles.subMenu}>
              {item.children.map((subItem) => (
                <div
                  key={subItem.key}
                  className={styles.subMenuItem}
                  onClick={(e) => handleSubMenuClick(e, subItem.path)}
                >
                  {subItem.name}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default MenuRender;
