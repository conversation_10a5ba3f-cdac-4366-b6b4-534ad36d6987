import React from 'react';
import styles from './index.module.sass';

interface WaterLevelProps {
  value: number;
  label?: React.ReactNode;
}

const Liquid: React.FC<WaterLevelProps> = ({ value, label }) => {
  const waterTop = value * 100 + 100;

  return (
    <div className={styles.container}>
      <div className={styles.water}>
        <div className={styles.before} style={{ top: `-${waterTop}%` }} />
        <div className={styles.after} style={{ top: `-${waterTop}%` }} />
      </div>
      {label && <div className={styles.label}>{label}</div>}
    </div>
  );
};

export default Liquid;
