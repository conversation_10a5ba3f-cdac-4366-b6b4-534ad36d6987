@import '~@/assets/css/helpers.sass'

.box
  display: flex
  flex-direction: row
  align-items: center
  justify-content: space-between

.container
  background-image: url('../../../../assets/bg/markettrading/bg4.png')
  background-size: 100% 100%
  width: px(210)
  height: px(120)
  background-repeat: no-repeat
  display: flex
  flex-direction: column
  align-items: start
  padding: px(20) 0 px(20) px(20)
  justify-content: space-between
  margin: px(10) px(10)
.title
  font-size: px(20)
  margin-bottom: px(5)

.value
  font-size: px(30)
  color: #20dbfd
  font-family: youshe
  font-weight: 500
  text-shadow: 0 0 px(1) #20dbfd, 0 0 px(20) #20dbfd
.filter
  display: flex
  flex-direction: column
  justify-content: center
  align-items: center

.dateContainer
  display: flex
  align-items: center
  gap: px(10)

.date
  width: px(120)

.dateWide
  width: px(220) !important
.btn
  :global
    .button___bVlQf
      height: px(36) !important
.tabs
  height: px(50)
  margin-right: px(10)
  display: flex
  justify-content: space-between
  div
    background-image: url(/tabs.png)
    background-repeat: no-repeat
    background-size: 100% 100%
    color: white
    font-size: px(18)
    font-weight: bold
    width: px(80)
    display: flex
    align-items: center
    justify-content: center
    height: px(36)
    cursor: pointer
    // box-shadow: 0 0 00
    span
      color: #1FD6F9
    &:hover
      // box-shadow: 0 0 px(5) px(4) #1FD6F9
      background-image: url(/acttabs.png)
  .active
    // box-shadow: 0 0 px(5) px(2) #1FD6F9
    background-image: url(/acttabs.png)
