// import { MicroPowerContext } from '@/pages/UserDetail/components/PowerInfo/MicroPowerContext';
import px from '@/utils/px';
import * as echarts from 'echarts';
import { useEffect } from 'react';
const GradientChart = ({ xAxisData, seriesDataArrays }) => {
  // const {startTime, endTime} = useContext(MicroPowerContext);
  useEffect(() => {
    const chartDom = document.getElementById('stacked-line-chart');
    const myChart = echarts.init(chartDom);

    const option = {
      color: ['#0077FF', 'rgb(136, 192, 255)'],
      title: {
        text: ' ',
        textStyle: {
          color: '#fff', // Title text color
          fontSize: px(10),
        },
      },
      dataZoom: [
        {
          type: 'inside', // 或 'inside'
          start: 0, // 开始位置（0-100）
          end: 100,
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        // formatter: function (params) {
        //   return params
        //     .map((param) => {
        //       let value = param.value;
        //       let seriesName = param.seriesName;

        //       // Add unit based on series name
        //       let unit = '';
        //       if (seriesName === '氢气') unit = ' unit (log2)'; // Replace 'unit' with actual unit
        //       if (seriesName === '甲醛') unit = ' unit'; // Replace 'unit' with actual unit
        //       if (seriesName === '乙醇') unit = ' unit (log2)'; // Indicate logarithmic value
        //       if (seriesName === '二氧化碳') unit = ' unit (log2)'; // Replace 'unit' with actual unit

        //       return `${seriesName}: ${value}${unit}`;
        //     })
        //     .join('<br/>');
        // },
      },
      legend: {
        left: 'center', // 图例居中
        top: '3%',
        data: ['储能功率', '电网进线功率'],
        textStyle: {
          color: '#fff', // Title text color
          fontSize: px(14), // 将字体大小调整
        },
      },
      toolbox: {
        // feature: {
        //   saveAsImage: {},
        // },
      },
      grid: {
        left: '3%',
        right: '10%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLabel: {
            color: 'rgba(216, 240, 255, 0.8)', // X axis labels color
            fontSize: px(12),
            interval: 0,
          },
        },
      ],
      yAxis: [
        {
          name: 'kW',
          type: 'value',
          axisLabel: {
            color: 'rgba(216, 240, 255, 0.8)', // Y axis labels color
            fontSize: px(12), // 减小Y轴标签字体大小至8px
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#304A65',
              type: 'dashed',
            },
          },
        },
      ],
      series: [
        // Add your series data here, following the format of the first series
        // Example for one series:
        {
          name: '储能功率',
          type: 'line',
          stack: 'Total',
          smooth: false,
          lineStyle: {
            width: px(1),
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0.2,
                color: 'rgb(42, 163, 224)',
              },
              {
                offset: 0.5,
                color: 'rgb(0, 56, 121)',
              },
              {
                offset: 0.8,
                color: 'rgb(0, 0, 0)',
              },
            ]),
          },
          emphasis: {
            focus: 'series',
          },
          data: seriesDataArrays[0],
        },
        {
          name: '电网进线功率',
          type: 'line',
          stack: 'Total',
          smooth: false,
          lineStyle: {
            width: px(1),
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(136, 192, 255, 0.68)',
              },
              {
                offset: 1,
                color: 'rgba(136, 192, 255, 0)',
              },
            ]),
          },
          emphasis: {
            focus: 'series',
          },
          data: seriesDataArrays[1],
        },

        // Repeat for each line (Line 2, Line 3, etc.)
      ],
    };

    myChart.setOption(option);

    return () => {
      myChart.dispose();
    };
  }, [xAxisData, seriesDataArrays]);

  return (
    <div
      id="stacked-line-chart"
      style={{ height: '100%', width: '100%' }}
    ></div>
  );
};

export default GradientChart;
