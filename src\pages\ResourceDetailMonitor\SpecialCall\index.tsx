import chargingStile from '@/assets/bg/chargeStile.png';
import {
  AGetCurves,
  AGetList,
  AGetTotalCurves,
} from '@/services/resourceMointor/specialCall';
import px from '@/utils/px';
import { DatePicker, Pagination, Spin, Tooltip } from 'antd';
import dayjs from 'dayjs';
import EChartsReact from 'echarts-for-react';
import { useEffect, useState } from 'react';
import ElecBar from './ElecBar';
import styles from './index.sass';
import { getLoadPredictionChartOption } from './option';
import TimeRangeModal from './TimeRangeModal';
export default function ChargintPile() {
  const [time, setTime] = useState(dayjs());
  const [selectedPile, setSelectedPile] = useState('');
  const [showTotal, setShowTotal] = useState(true);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [timeRangeModalOpen, setTimeRangeModalOpen] = useState(false);
  const [lineValue, setLineValue] = useState({
    dateStringList: [],
    powerRateList: [],
    timeList: [],
    powerList: [],
    baseline: [],
    upList: [],
    downList: [],
  });
  const [code, setCode] = useState('');

  const getStationData = (curr = 1) => {
    AGetList({
      pageSize: 8,
      current: curr,
    }).then((res) => {
      if (res?.list?.[0]) {
        setData(res.list || []);
        setTotal(res.total);
        setSelectedPile(res.list[0]);
      }
    });
  };
  useEffect(() => {
    getStationData();
    AGetTotalCurves({ dayStr: time.format('YYYY-MM-DD HH:mm:ss') }).then(
      (res) => {
        setLineValue(res);
      },
    );
  }, []);
  const onPageChange = (page) => {
    setCurrent(page);
    getStationData(page);
  };
  const onViewPower = (code, info) => {
    setLoading(true);
    setShowTotal(false);
    setSelectedPile(info);
    setCode(code);
    setLineValue({
      dateStringList: [],
      powerRateList: [],
      timeList: [],
      powerList: [],
      baseline: [],
      upList: [],
      downList: [],
    });
    AGetCurves({
      code,
      dayStr: time.format('YYYY-MM-DD HH:mm:ss'),
    }).then((power) => {
      setLineValue(power);
      setLoading(false);
    });
  };
  const onViewTotal = () => {
    setShowTotal(true);
    setLoading(true);
    setLineValue({
      dateStringList: [],
      powerRateList: [],
      timeList: [],
      powerList: [],
      baseline: [],
      upList: [],
      downList: [],
    });
    AGetTotalCurves({ dayStr: time.format('YYYY-MM-DD HH:mm:ss') }).then(
      (res) => {
        setLineValue(res);
        setLoading(false);
      },
    );
  };

  const handleTimeRangeConfirm = (startDate: string, endDate: string) => {
    console.log('选择的时间范围:', startDate, '到', endDate);
    // 这里可以添加获取历史数据的逻辑
    setTimeRangeModalOpen(false);
  };

  return (
    <div className={styles.page}>
      <Spin spinning={loading} fullscreen tip={'数据加载中···'}></Spin>
      <div className={styles.top}>
        <div className={styles.table}>
          {data?.map((item, index) => (
            <div key={index} className={styles.card}>
              <div className={styles.cardTitle}>
                <Tooltip title={item.name}>
                  <div className={styles.titleText}>{item.name}</div>
                </Tooltip>
              </div>
              <div key={index} className={styles.floor}>
                <div className={styles.icon}>
                  <img src={chargingStile} alt={item.name} />
                </div>
                <div className={styles.content}>
                  <div className={styles.power}>
                    <div className={styles.label}>电站容量：</div>
                    <div className={styles.value}>{item.capcity}kW</div>
                  </div>
                  <div className={styles.power}>
                    <div className={styles.label}>电站地址：</div>
                    <div className={styles.value}>{item.addr}</div>
                  </div>
                  <div className={styles.power}>
                    <div className={styles.label}>区域编码：</div>
                    <div className={styles.value}>{item.region}</div>
                  </div>
                  <div className={styles.power}>
                    <div className={styles.label}>操作：</div>
                    <div
                      className={styles.view}
                      onClick={() => onViewPower(item.code, item)}
                    >
                      查看当日功率曲线与电量详情
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
          {!data?.length && (
            <div className={styles.none}>
              未获取到任何数据
              <img src="/empty.png" alt="" />
            </div>
          )}
        </div>
        <Pagination
          className={styles.pagination}
          current={current}
          total={total}
          pageSize={8}
          showSizeChanger={false}
          onChange={onPageChange}
        ></Pagination>
      </div>
      <div className={styles.bottom}>
        <div className={styles.bottomLeft}>
          <div className={styles.title}>
            {showTotal ? '整体曲线' : `${selectedPile?.name}--功率曲线`}
          </div>
          <div
            className={styles.total2}
            onClick={() => setTimeRangeModalOpen(true)}
          >
            历史数据补招
          </div>
          <div className={styles.total} onClick={onViewTotal}>
            整体曲线
          </div>
          <DatePicker
            value={time}
            allowClear={false}
            onChange={(v) => {
              setLoading(true);
              setTime(v);
              if (showTotal) {
                AGetTotalCurves({
                  dayStr: v.format('YYYY-MM-DD HH:mm:ss'),
                }).then((res) => {
                  setLineValue(res);
                  setLoading(false);
                });
              } else {
                AGetCurves({
                  code: selectedPile?.code,
                  dayStr: v.format('YYYY-MM-DD HH:mm:ss'),
                }).then((power) => {
                  setLineValue(power);
                  setLoading(false);
                });
              }
            }}
          ></DatePicker>
          {!lineValue?.timeList?.length && (
            <div className={styles.none}>
              未获取到任何数据
              <img src="/empty.png" alt="" />
            </div>
          )}
          {lineValue?.timeList?.length ? (
            <EChartsReact
              style={{
                height: px(320),
              }}
              option={getLoadPredictionChartOption(lineValue)}
            />
          ) : (
            ''
          )}
        </div>
        <div className={styles.bottomRight}>
          <div className={styles.title}>{selectedPile?.name}--电量详情</div>
          <ElecBar code={code} />
        </div>
      </div>
      <TimeRangeModal
        open={timeRangeModalOpen}
        onCancel={() => setTimeRangeModalOpen(false)}
        onConfirm={handleTimeRangeConfirm}
      />
    </div>
  );
}
