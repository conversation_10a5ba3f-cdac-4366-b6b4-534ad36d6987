import { isLongScreen, openPage } from '@/utils/util';
import { history, useModel } from '@umijs/max';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import CustomDropDown from '../CustomDropDown';

import styles from './index.sass';
import UnitType from './UnitType';
import UserMenu from './UserMenu';
export default function (prop: any) {
  const { menuData: menuD } = prop;
  const {
    initialState: { permission },
  } = useModel('@@initialState');
  const pageList = permission.availablePageList || [];
  const excluedMenus = ['数据监测'];
  const hoverMenus = ['资源管理', '辅助决策', '市场交易'];

  const menuData = menuD?.filter(
    (item: any) =>
      item.path !== '/home' && item.name && !excluedMenus.includes(item.name),
  );
  const leftMenuData = menuData.slice(0, 3);
  const rightMenuData = menuData.slice(3, 6);

  let messageInter;

  const [year, setYear] = useState('');
  const [time, setTime] = useState('');
  useEffect(() => {
    // const weeks = ['日', '一', '二', '三', '四', '五', '六'];
    const timeinter = setInterval(() => {
      const year = dayjs().format('YYYY-MM-DD');
      const time = dayjs().format('HH:mm:ss');
      setYear(year);
      setTime(time);
    }, 1000);

    return () => {
      clearInterval(timeinter);
      clearInterval(messageInter);
    };
  }, []);

  const onClickMenu = (item: any) => {
    if (
      pageList.includes(item?.path?.slice(1)) ||
      pageList.includes(item?.path?.match(/\/([^/]+)\//)?.[1])
    ) {
      openPage(item.path);
    } else {
      history.push('/error');
    }
  };

  return (
    <div className={styles.head}>
      {isLongScreen ? (
        <div className={styles.longHead}>
          <div className={styles.longLogo}>
            <img src="/logo2.png" alt="" />
          </div>
          <div className={styles.longTime}>
            {dayjs().format('YYYY-MM-DD  HH:mm:ss')}
          </div>
          <div className={styles.longHeadTitle}>
            华电山东虚拟电厂协同管控平台
          </div>
          <div className={styles.longTool}>
            {location.href.includes('resource-monitor') && (
              <>
                <img
                  src="/cloud.png"
                  alt="天气"
                  className={styles.weatherIcon}
                />
                多云
              </>
            )}
            {!location.href.includes('resource-monitor') &&
              !location.href.includes('long-trading') && <UnitType></UnitType>}
            <UserMenu></UserMenu>
          </div>
        </div>
      ) : (
        <>
          <div className={styles.logo}>
            <img src="/logo2.png" alt="" />
          </div>
          <div className={styles.time}>
            <div className={styles.date}>{time}</div>
            <div className={styles.year}>{year}</div>
          </div>
          <div className={styles.leftBox}>
            {leftMenuData.map((item: any) => {
              if (!item.name) return null;
              const isActive = item.path === prop.matchMenuKeys[0];
              return (
                <div
                  key={item.key}
                  className={classNames(
                    styles.menuLeft,
                    isActive ? styles.menuLeftActive : null,
                  )}
                  onClick={() =>
                    !hoverMenus.includes(item.name) && onClickMenu(item)
                  }
                >
                  {hoverMenus.includes(item.name) ? (
                    <CustomDropDown
                      onClick={onClickMenu}
                      routerName={item.name}
                    />
                  ) : (
                    item.name
                  )}
                </div>
              );
            })}
          </div>
          <div className={styles.title}>华电山东虚拟电厂协同管控平台</div>
          <div className={styles.rightBox}>
            {rightMenuData.map((item: any) => {
              if (!item.name) return null;
              const isActive = item.path === prop.matchMenuKeys[0];
              return (
                <div
                  key={item.key}
                  className={classNames(
                    styles.menuRight,
                    isActive ? styles.menuRightActive : null,
                  )}
                  onClick={() =>
                    !hoverMenus.includes(item.name) && onClickMenu(item)
                  }
                >
                  {hoverMenus.includes(item.name) ? (
                    <CustomDropDown
                      onClick={onClickMenu}
                      routerName={item.name}
                    />
                  ) : (
                    item.name
                  )}
                </div>
              );
            })}
          </div>
          <div className={styles.tool}>
            {location.href.includes('resource-monitor') && (
              <>
                <img
                  src="/cloud.png"
                  alt="天气"
                  className={styles.weatherIcon}
                />
                多云
              </>
            )}
            {!location.href.includes('resource-monitor') &&
              !location.href.includes('long-trading') && <UnitType></UnitType>}
            <UserMenu></UserMenu>
          </div>
        </>
      )}
    </div>
  );
}
