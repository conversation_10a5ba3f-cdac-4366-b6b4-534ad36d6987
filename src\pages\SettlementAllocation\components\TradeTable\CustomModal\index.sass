@import '~@/assets/css/helpers.sass'
.stepsContainer
  margin-bottom: px(30)
  padding: px(20) px(30)
  background: linear-gradient(135deg, rgba(12, 67, 126, 0.3), rgba(29, 92, 158, 0.2))
  border-radius: px(12)
  border: 1px solid rgba(71, 114, 255, 0.3)
  backdrop-filter: blur(px(10))

.customSteps
  padding: px(10) 0
  overflow: visible
  :global
    .ant-steps-item
      cursor: pointer
      transition: all 0.3s ease
      overflow: visible

      &:hover
        transform: translateY(-px(2))

      .ant-steps-item-icon
        line-height: px(28)
        background: linear-gradient(135deg, #4987D4, #2F5F8F)
        border-color: #4987D4
        color: #fff
        box-shadow: 0 px(4) px(12) rgba(73, 135, 212, 0.3)
        transition: all 0.3s ease
        overflow: visible
        position: relative

        &:hover
          box-shadow: 0 px(6) px(16) rgba(73, 135, 212, 0.5)
          transform: scale(1.2)
          z-index: 10

      .ant-steps-item-title
        color: #94EFFF !important
        font-weight: 600
        font-size: px(16)
        text-shadow: 0 0 px(8) rgba(148, 239, 255, 0.5)

      .ant-steps-item-description
        color: rgba(148, 239, 255, 0.8) !important
        font-size: px(14)

    .ant-steps-item-active
      .ant-steps-item-icon
        background: linear-gradient(135deg, #00D4FF, #0099CC) !important
        border-color: #00D4FF !important
        box-shadow: 0 px(6) px(20) rgba(0, 212, 255, 0.6) !important
        animation: pulse 2s infinite

      .ant-steps-item-title
        color: #00D4FF !important

    .ant-steps-item-finish
      .ant-steps-item-icon
        background: linear-gradient(135deg, #52C41A, #389E0D) !important
        border-color: #52C41A !important

      .ant-steps-item-title
        color: #52C41A !important

    .ant-steps-item-tail::after
      background: linear-gradient(90deg, #4987D4, #00D4FF)
      height: px(2)

@keyframes pulse
  0%
    box-shadow: 0 px(6) px(20) rgba(0, 212, 255, 0.6)
  50%
    box-shadow: 0 px(8) px(25) rgba(0, 212, 255, 0.8)
  100%
    box-shadow: 0 px(6) px(20) rgba(0, 212, 255, 0.6)

.operationInfo
  color: #94EFFF
  font-size: px(16)
  line-height: 1.8

  p
    margin-bottom: px(10)
    padding: px(8) px(15)
    background: rgba(73, 135, 212, 0.2)
    border-radius: px(6)
    border-left: 3px solid #4987D4

.hourlyData
  color: #94EFFF
  font-size: px(16)
  line-height: 1.8

  p
    margin-bottom: px(10)
    padding: px(8) px(15)
    background: rgba(73, 135, 212, 0.2)
    border-radius: px(6)
    border-left: 3px solid #4987D4

.chart
  display: flex
  flex-direction: row
  width: 100%
  height: px(400)
  justify-content: space-between
  margin-bottom: px(20)
.title
  font-size: px(16)
  text-shadow: 0 0 px(10) rgb(5, 88, 243), px(2) px(2) px(10) rgb(5, 88, 243)
  transform: translate(0, -px(10))
