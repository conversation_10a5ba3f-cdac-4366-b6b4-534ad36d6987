import Tab from '@/components/Tab';
import { AGetRPrice } from '@/services/decisionSupport/priceForecastController';
import px from '@/utils/px';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import { option } from './option';

interface Props {
  dateRange: [dayjs.Dayjs, dayjs.Dayjs];
}

export default function RPriceLine({ dateRange }: Props) {
  const container = useRef<HTMLDivElement>(null);
  const myChart = useRef<any>();

  useEffect(() => {
    if (!myChart.current && container.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
    }

    const startDayStr = dateRange[0].format('YYYY-MM-DD');
    const endDayStr = dateRange[1].format('YYYY-MM-DD');

    AGetRPrice({ startDayStr, endDayStr }).then((res) => {
      myChart.current.setOption(option(res.data));
    });
  }, [dateRange]);

  return (
    <>
      <Tab title="2R机组电价"></Tab>
      <div
        style={{
          width: '100%',
          height: px(360),
        }}
        ref={container}
      ></div>
    </>
  );
}
