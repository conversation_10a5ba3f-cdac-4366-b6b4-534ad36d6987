import classnames from 'classnames';
import styles from './index.module.sass';
interface StyledBoxProps {
  keyId: string;
  icon: string;
  title: string;
  className?: string;
  children: React.ReactNode;
  right?: React.ReactNode;
}

const StyledBox = ({
  keyId,
  icon,
  title,
  children,
  right,
  className,
}: StyledBoxProps) => {
  return (
    <div
      className={classnames(styles.box, className)}
      key={keyId}
      w="full"
      h="full"
      p="4"
    >
      <div className={styles.header}>
        <div className={styles.left}>
          <div className={styles.icon}>
            <img src={icon} alt="icon" w="30px" h="30px"></img>
          </div>
          <div className={styles.title}>{title}</div>
        </div>
        <div className={styles.right}>{right}</div>
      </div>
      <div className={styles.content}>{children}</div>
    </div>
  );
};

export default StyledBox;
