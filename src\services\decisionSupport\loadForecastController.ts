// @ts-ignore
/* eslint-disable */
import { mock } from '@/utils/util';
import { message } from 'antd';
import axios from 'axios';
const api = '/decisionSupport';

// 创建一个新的 axios 实例，不继承全局配置
const customAxios = axios.create({
  baseURL: '/',
});

/** 获取抽蓄总出力预测数据*/
export const getPumpedStorage = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], pumpedList: [], unit: '' } });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predict/pumpedStorage', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/** 获取抽蓄总出力预测数据*/
export const getPumpedStorageV3 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], pumpedList: [], unit: '' } });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predictV3/pumpedStorage', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/** 获取新能源总出力预测数据*/
export const getNewEnergy = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], wind: [], pv: [], unit: '' } });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predict/newEnergyMarket', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/** 获取新能源总出力预测数据*/
export const getNewEnergyV3 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], wind: [], pv: [], unit: '' } });
    });
  } else {
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predictV3/newEnergyMarket', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取直调公用单日数据 */
export const getDirectlyPublic = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], totalPowerList: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predict/directlyPublicPrediction', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取直调公用连续日期数据 */
export const getDirectlyPublicV3 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], valueList: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predictV3/directlyPublicPrediction', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取联络线单日数据 */
export const getConnectLines = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], nonMarketList: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predict/connectLinesLoadPrediction', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取联络线连续日期数据 */
export const getConnectLinesV3 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], nonMarketList: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predictV3/connectLinesLoadPrediction', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取直调用电单日数据 */
export const getDirectlyElectrical = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], direct: [], entire: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predict/drectlyElectricalLoadPrediction', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取直调用电连续日期数据 */
export const getDirectlyElectricalV3 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], valueList: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predictV3/drectlyElectricalLoadPrediction', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取现货价格预测数据 */
export const getDailyPrice = (params: {
  dateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], downPowerList: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predict/dayAheadPricePrediction', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

/* 获取现货价格连续日期数据 */
export const getDailyPriceV3 = (params: {
  startDateStr: string;
  endDateStr: string;
}): Promise<{ data: any }> => {
  // 如果mock为true，则返回一个Promise，resolve为true
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: { timeList: [], downPowerList: [], unit: '' } });
    });
  } else {
    // 否则，返回一个Promise，使用customAxios发送get请求，获取电力信息
    return new Promise((resolve) => {
      customAxios
        .get(api + '/predictV3/dayAheadPricePrediction', {
          params: params,
        })
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

export const exportExcel = (params: { dateStr: string }) => {
  axios
    .get(api + '/predict/exportExcel', {
      responseType: 'blob',
      params,
    })
    .then((res) => {
      const data = res.data;
      const fileName = `预测表_${params.dateStr}`;
      const blob = new Blob([data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      if (window.navigator && (window.navigator as any).msSaveOrOpenBlob) {
        (window.navigator as any).msSaveBlob(blob, fileName);
      } else {
        let u = window.URL.createObjectURL(blob);
        let aLink = document.createElement('a');
        aLink.style.display = 'none';
        aLink.href = u;
        aLink.setAttribute('download', fileName + '.xlsx');
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink);
        window.URL.revokeObjectURL(u);
      }
    })
    .catch((error) => {
      console.error('Export failed:', error);
      message.error('导出失败，请重试');
    });
};
