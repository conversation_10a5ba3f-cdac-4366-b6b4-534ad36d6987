import CustomModal from '@/components/CustomModal';
import React from 'react';
import styles from './index.sass';
interface Props {
  open: boolean;
  onCancel: () => void;
}
const Index: React.FC<Props> = ({ open, onCancel }) => {
  return (
    <CustomModal
      open={open}
      footer={null}
      onCancel={onCancel}
      className={styles.box}
      centered={true}
    >
      <span>正在生成调度计划中</span>
      <img src="/loading.svg" className={styles.img}></img>
    </CustomModal>
  );
};

export default Index;
