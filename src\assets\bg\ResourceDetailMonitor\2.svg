<svg width="86.000000" height="92.000000" viewBox="0 0 86 92" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_195_301_dd" x="0.000000" y="58.454590" width="86.000000" height="33.545410" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="4"/>
			<feGaussianBlur stdDeviation="3.33333"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<linearGradient id="paint_linear_195_300_0" x1="91.107841" y1="62.294651" x2="91.107841" y2="10.000000" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0077FF" stop-opacity="0.301961"/>
			<stop offset="0.572145" stop-color="#4593EC" stop-opacity="0.031373"/>
			<stop offset="1.000000" stop-color="#85BCFB" stop-opacity="0.000000"/>
		</linearGradient>
		<radialGradient id="paint_Diamond_195_301_0" cx="0.000000" cy="0.000000" r="1.000000" gradientUnits="userSpaceOnUse" gradientTransform="translate(43 75.2273) rotate(0) scale(43 110.239)">
			<stop stop-color="#0077FF"/>
			<stop stop-color="#0077FF" stop-opacity="0.027451"/>
			<stop offset="1.000000" stop-color="#66CFFF"/>
		</radialGradient>
		<pattern id="pattern_195_2760" patternContentUnits="objectBoundingBox" width="1.000000" height="1.000000">
			<use xlink:href="#image195_276_0" transform="matrix(0.007813,0,0,0.007813,0,0)"/>
		</pattern>
		<image id="image195_276_0" width="128.000000" height="128.000000" xlink:href="data:image/png;base64,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"/>
	</defs>
	<rect id="组件 8" width="86.000000" height="82.000000" transform="translate(0.000000 10.000000)" fill="#FFFFFF" fill-opacity="0"/>
	<rect id="组件 6" width="86.000000" height="82.000000" transform="translate(0.000000 10.000000)" fill="#FFFFFF" fill-opacity="0"/>
	<path id="矩形" d="M0 10L86 10L86 75.2273C86 75.2273 82.9763 67.8284 80.8732 65.9091C78.7589 63.9796 75.5494 64.5786 73.5834 63.6979C64.0002 59.4045 48.9493 58.4546 43 58.4546C35.9084 58.4546 23.2086 58.4546 10.569 63.6979C8.9035 64.3887 4.33679 65.5626 2.37677 67.8109C0.765991 69.6586 0 75.2273 0 75.2273L0 10Z" fill="url(#paint_linear_195_300_0)" fill-opacity="1.000000" fill-rule="nonzero"/>
	<g filter="url(#filter_195_301_dd)">
		<ellipse id="椭圆 1" cx="43.000000" cy="75.227295" rx="43.000000" ry="16.772728" fill="url(#paint_Diamond_195_301_0)" fill-opacity="1.000000"/>
	</g>
	<rect id="功率" x="8.000000" width="70.000000" height="70.000000" fill="url(#pattern_195_2760)" fill-opacity="1.000000"/>
</svg>
