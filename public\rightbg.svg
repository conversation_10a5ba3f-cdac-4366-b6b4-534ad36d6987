<svg width="540.328613" height="1564.000000" viewBox="0 0 540.329 1564" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_43_4844_dd" x="514.635742" y="484.994629" width="4.984863" height="108.133301" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="1" dy="-1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.32 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4845_dd" x="514.635742" y="967.988770" width="4.984863" height="108.133301" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="1" dy="-1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.32 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4847_dd" x="464.328613" y="604.000000" width="76.000000" height="355.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="3" dy="1"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.1451 0 0 0 0 0.32157 0 0 0 0 0.46667 0 0 0 0.61 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-2"/>
			<feGaussianBlur stdDeviation="1"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.31 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_2"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="1"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.23137 0 0 0 0 1 0 0 0 0 0.95686 0 0 0 0.61 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_2" result="effect_innerShadow_3"/>
		</filter>
		<filter id="filter_43_4848_dd" x="484.328613" y="626.000000" width="35.000000" height="309.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="2" dy="-1"/>
			<feGaussianBlur stdDeviation="1.33333"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.57 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4849_dd" x="505.912109" y="652.240234" width="3.738770" height="256.635742" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="1" dy="-1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.32 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4853_dd" x="467.498535" y="55.979004" width="40.015137" height="93.772949" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="1" dy="-1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.32 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4850_dd" x="467.498535" y="55.979004" width="40.015137" height="93.772949" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4851_dd" x="483.003418" y="55.979004" width="24.494141" height="56.287109" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4852_dd" x="496.728027" y="55.979004" width="10.779785" height="25.980957" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4854_dd" x="484.104004" y="1465.401367" width="23.677734" height="54.787598" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1" dy="-4"/>
			<feGaussianBlur stdDeviation="3.66667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.07451 0 0 0 0 0.16078 0 0 0 0 0.24314 0 0 0 0.51 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="1" dy="-1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.60392 0 0 0 0 0.95294 0 0 0 0 1 0 0 0 0.32 0"/>
			<feBlend mode="normal" in2="effect_innerShadow_1" result="effect_innerShadow_2"/>
		</filter>
		<filter id="filter_43_4856_dd" x="496.328613" y="652.000000" width="13.000000" height="257.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4860_dd" x="436.301758" y="32.000000" width="71.026855" height="117.714844" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4857_dd" x="436.301758" y="32.000000" width="71.026855" height="117.714844" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4858_dd" x="463.823730" y="32.000000" width="43.476562" height="70.658203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4859_dd" x="488.185059" y="32.000000" width="19.133789" height="32.614258" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4861_dd" x="436.328613" y="1465.000000" width="71.000000" height="71.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_43_4862_dd" x="175.328613" y="77.500000" width="155.500000" height="89.500000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0.666667"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<linearGradient x1="299.188965" y1="257.500092" x2="74.328598" y2="257.500092" id="paint_linear_43_4804_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0B447C"/>
			<stop offset="1.000000" stop-color="#3397F9"/>
		</linearGradient>
		<linearGradient x1="151.560089" y1="341.105469" x2="151.560089" y2="1311.030396" id="paint_linear_43_4805_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0B3C70"/>
			<stop offset="0.492269" stop-color="#329BF9"/>
			<stop offset="1.000000" stop-color="#0B3D72"/>
		</linearGradient>
		<linearGradient x1="168.414093" y1="341.105469" x2="168.414093" y2="1311.030396" id="paint_linear_43_4806_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#040E2B"/>
			<stop offset="0.492269" stop-color="#459AED"/>
			<stop offset="1.000000" stop-color="#040E2B"/>
		</linearGradient>
		<linearGradient x1="131.528168" y1="368.547302" x2="131.528168" y2="1298.163330" id="paint_linear_43_4840_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0B457E"/>
			<stop offset="0.538071" stop-color="#299FFF"/>
			<stop offset="1.000000" stop-color="#0B447C"/>
		</linearGradient>
		<linearGradient x1="428.694641" y1="2.000000" x2="428.694641" y2="1562.000000" id="paint_linear_43_4843_0" gradientUnits="userSpaceOnUse">
			<stop offset="0.000929" stop-color="#0A447B"/>
			<stop offset="0.520487" stop-color="#0B457E"/>
			<stop offset="0.992366" stop-color="#0B447C"/>
		</linearGradient>
		<linearGradient x1="519.871216" y1="490.684174" x2="516.108521" y2="596.789856" id="paint_linear_43_4844_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#29384A"/>
			<stop offset="1.000000" stop-color="#1B2432"/>
		</linearGradient>
		<linearGradient x1="519.871277" y1="1070.432373" x2="516.108582" y2="964.326599" id="paint_linear_43_4845_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#29384A"/>
			<stop offset="1.000000" stop-color="#1B2432"/>
		</linearGradient>
		<linearGradient x1="168.296066" y1="2.000000" x2="53.769844" y2="51.792633" id="paint_linear_43_4846_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FF2B2B" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#7EFFEF" stop-opacity="0.501961"/>
		</linearGradient>
		<linearGradient x1="526.346191" y1="608.999939" x2="496.355042" y2="950.491455" id="paint_linear_43_4847_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#040E2B"/>
			<stop offset="0.492269" stop-color="#040E2B"/>
			<stop offset="1.000000" stop-color="#040E2B"/>
		</linearGradient>
		<linearGradient x1="509.356689" y1="626.000000" x2="509.356689" y2="935.000000" id="paint_linear_43_4848_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0E434C"/>
			<stop offset="0.512839" stop-color="#5597B2"/>
			<stop offset="1.000000" stop-color="#0D3844"/>
		</linearGradient>
		<linearGradient x1="509.839508" y1="665.743469" x2="507.016632" y2="917.567688" id="paint_linear_43_4849_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#29384A"/>
			<stop offset="1.000000" stop-color="#1B2432"/>
		</linearGradient>
		<linearGradient x1="509.525146" y1="60.912983" x2="479.321136" y2="152.927856" id="paint_linear_43_4853_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#29384A"/>
			<stop offset="1.000000" stop-color="#1B2432"/>
		</linearGradient>
		<linearGradient x1="508.971924" y1="1468.284058" x2="491.099731" y2="1522.044312" id="paint_linear_43_4854_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#29384A"/>
			<stop offset="1.000000" stop-color="#1B2432"/>
		</linearGradient>
	</defs>
	<path id="路径" d="M255.32 257.5C255.32 259.72 253.59 261.47 251.38 261.5L267.8 1410.1L267.8 1407.52L267.82 1407.5C270.06 1407.5 271.82 1409.26 271.82 1411.5C271.82 1413.74 270.06 1415.5 267.82 1415.5L267.8 1415.48L267.8 1411.5L133.82 1411.5L74.32 1308.06C74.32 1308.06 151.76 1075.62 151.76 815.97C151.76 556.3 74.32 344.73 74.32 344.73L117.82 257.5L251.3 257.5L251.3 253.52L251.32 253.5C253.56 253.5 255.32 255.26 255.32 257.5Z" fill="#D8D8D8" fill-opacity="0" fill-rule="evenodd"/>
	<path id="路径" d="M251.32 257.5L117.82 257.5L74.32 344.73C74.32 344.73 151.76 556.3 151.76 815.97C151.76 1075.62 74.32 1308.06 74.32 1308.06L133.82 1411.5L267.82 1411.5" stroke="url(#paint_linear_43_4804_0)" stroke-opacity="1.000000" stroke-width="8.000000" stroke-linejoin="round" stroke-linecap="round" stroke-dasharray="0 0"/>
	<g opacity="0.800000">
		<path id="路径" d="M82.65 341.1C134.51 464.29 164.6 632.47 164.6 818.01C164.6 1011.8 137.96 1149.64 86.88 1311.03L82.65 341.1Z" fill="#D8D8D8" fill-opacity="0" fill-rule="evenodd"/>
		<path id="路径" d="M82.65 341.1C134.51 464.29 164.6 632.47 164.6 818.01C164.6 1011.8 137.96 1149.64 86.88 1311.03" stroke="url(#paint_linear_43_4805_0)" stroke-opacity="1.000000" stroke-width="4.000000" stroke-dasharray="12 4"/>
	</g>
	<path id="路径" d="M99.5 341.1C151.37 464.29 181.45 632.47 181.45 818.01C181.45 1011.8 154.81 1149.64 103.74 1311.03L99.5 341.1Z" fill="#D8D8D8" fill-opacity="0" fill-rule="evenodd"/>
	<path id="路径" d="M99.5 341.1C151.37 464.29 181.45 632.47 181.45 818.01C181.45 1011.8 154.81 1149.64 103.74 1311.03" stroke="url(#paint_linear_43_4806_0)" stroke-opacity="1.000000" stroke-width="5.000000" stroke-dasharray="0 0"/>
	<g opacity="0.800000">
		<path id="形状结合" d="M93.8267 1111.93C86.1782 1145.06 77.2256 1176.9 67.043 1207.16L79.7837 1230.26C90.6685 1197.94 100.229 1163.89 108.383 1128.42L93.8267 1111.93ZM115.428 988.986C110.89 1025.49 104.903 1061.01 97.5454 1095.24L112.096 1111.72C119.954 1075.15 126.339 1037.17 131.161 998.12L115.428 988.986ZM125.147 862.047C124.023 898.99 121.468 935.357 117.553 970.866L133.279 979.995C137.447 942.179 140.162 903.433 141.344 864.066L125.147 862.047ZM122.978 729.077C124.85 758.542 125.805 788.461 125.805 818.687C125.805 826.916 125.734 835.123 125.593 843.304L141.79 845.323C141.946 836.472 142.024 827.592 142.024 818.687C142.024 786.49 141.002 754.628 139 723.261L122.978 729.077ZM109.167 604.268C114.686 638.758 118.879 674.28 121.675 710.549L137.699 704.732C134.736 666.073 130.277 628.225 124.401 591.497L109.167 604.268ZM83.7847 485.405C92.6016 517.848 100.11 551.735 106.235 586.76L121.477 573.982C114.955 536.505 106.94 500.269 97.5195 465.61L83.7847 485.405ZM48.7798 380.863C60.1558 408.856 70.3755 438.547 79.3687 469.654L93.1079 449.853C83.5107 416.556 72.5903 384.807 60.4253 354.917L48.7798 380.863ZM74.7139 1244.91L61.9712 1221.81C56.3013 1237.74 50.2817 1253.2 43.9238 1268.15L42.3286 1271.88L53.8433 1298.16C61.2275 1281.05 68.1899 1263.28 74.7139 1244.91Z" clip-rule="evenodd" fill="url(#paint_linear_43_4840_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="矩形" d="M6.32 46.5L146.32 46.5L183.32 188.5L315.82 188.5L362.82 97L442.99 97L479.24 188.5L507.79 250L507.79 298.5L507.79 461.5L507.79 494.33L479.24 585.5L442.99 671L442.99 925.5L479.24 989L479.24 1080L479.24 1109.5L479.24 1253L507.79 1288.5L507.79 1376L385.82 1500L67.32 1562L113.82 1562L385.82 1562L533.32 1562L533.32 2L69.7 2L6.32 46.5Z" fill="url(#paint_linear_43_4843_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="矩形" d="M146.32 46.5L183.32 188.5L315.82 188.5L362.82 97L442.99 97L479.24 188.5L507.79 250L507.79 298.5L507.79 461.5L507.79 494.33L479.24 585.5L442.99 671L442.99 925.5L479.24 989L479.24 1080L479.24 1109.5L479.24 1253L507.79 1288.5L507.79 1376L385.82 1500L67.32 1562L113.82 1562L385.82 1562L533.32 1562L533.32 2L69.7 2L6.32 46.5L146.32 46.5Z" stroke="#2194F2" stroke-opacity="1.000000" stroke-width="4.000000"/>
	<g opacity="0.600000" filter="url(#filter_43_4844_dd)">
		<path id="矩形" d="M519.62 484.99L514.63 496.53L514.63 593.12L519.62 593.12L519.62 484.99Z" fill="url(#paint_linear_43_4844_0)" fill-opacity="0.500000" fill-rule="evenodd"/>
	</g>
	<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g opacity="0.600000" filter="url(#filter_43_4845_dd)">
		<path id="矩形" d="M519.62 1076.12L514.63 1064.57L514.63 967.98L519.62 967.98L519.62 1076.12Z" fill="url(#paint_linear_43_4845_0)" fill-opacity="0.500000" fill-rule="evenodd"/>
	</g>
	<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g opacity="0.600000" filter="url(#filter_43_4847_dd)">
		<path id="矩形" d="M531.32 609L496.79 609L467.32 654.85L467.32 907.58L496.79 952L531.32 952L531.32 609Z" fill="url(#paint_linear_43_4847_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4848_dd)">
		<path id="矩形" d="M519.32 626L506.61 626L484.32 656.52L484.32 905.89L506.61 935L519.32 935L519.32 626Z" fill="url(#paint_linear_43_4848_0)" fill-opacity="0.500000" fill-rule="evenodd"/>
	</g>
	<path id="矩形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g opacity="0.600000" filter="url(#filter_43_4849_dd)">
		<path id="左侧灯光-暗" d="M509.65 652.24L505.91 660.88L505.91 900.22L509.65 908.87L509.65 652.24Z" fill="url(#paint_linear_43_4849_0)" fill-opacity="0.500000" fill-rule="evenodd"/>
	</g>
	<path id="左侧灯光-暗" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g opacity="0.600000" filter="url(#filter_43_4853_dd)">
		<mask id="mask_43_4853" fill="white">
			<path id="左上角-暗" d="M507.498 102.087L507.498 112.266L483.004 55.979L486.936 55.979L507.498 102.087ZM507.508 81.96L507.508 71.8384L500.654 55.979L496.729 55.979L507.508 81.96ZM507.514 139.616L507.495 149.752L467.499 55.979L471.374 55.979L507.514 139.616Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
		</mask>
		<path id="左上角-暗" d="M507.498 102.087L507.498 112.266L483.004 55.979L486.936 55.979L507.498 102.087ZM507.508 81.96L507.508 71.8384L500.654 55.979L496.729 55.979L507.508 81.96ZM507.514 139.616L507.495 149.752L467.499 55.979L471.374 55.979L507.514 139.616Z" clip-rule="evenodd" fill="url(#paint_linear_43_4853_0)" fill-opacity="0.500000" fill-rule="evenodd" mask="url(#mask_43_4853)"/>
	</g>
	<path id="左上角-暗" d="" clip-rule="evenodd" fill="#979797" fill-opacity="0.000000" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4854_dd)">
		<path id="左侧三角-暗" d="M507.78 1465.4L484.1 1520.19L507.78 1520.19L507.78 1465.4Z" fill="url(#paint_linear_43_4854_0)" fill-opacity="0.500000" fill-rule="evenodd"/>
	</g>
	<path id="左侧三角-暗" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4856_dd)">
		<path id="左侧灯光-亮" d="M509.32 652L496.32 660.66L496.32 900.33L509.32 909L509.32 652Z" fill="#299FFF" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="左侧灯光-亮" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4860_dd)">
		<mask id="mask_43_4860" fill="white">
			<path id="左上角-亮" d="M507.319 64.6143L507.319 51.9087L495.153 32L488.185 32L507.319 64.6143ZM507.3 89.8804L507.3 102.658L463.824 32L470.803 32L507.3 89.8804ZM507.329 136.992L507.295 149.715L436.302 32L443.181 32L507.329 136.992Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
		</mask>
		<path id="左上角-亮" d="M507.319 64.6143L507.319 51.9087L495.153 32L488.185 32L507.319 64.6143ZM507.3 89.8804L507.3 102.658L463.824 32L470.803 32L507.3 89.8804ZM507.329 136.992L507.295 149.715L436.302 32L443.181 32L507.329 136.992Z" clip-rule="evenodd" fill="#2194F2" fill-opacity="1.000000" fill-rule="evenodd" mask="url(#mask_43_4860)"/>
	</g>
	<path id="左上角-亮" d="" clip-rule="evenodd" fill="#979797" fill-opacity="0.000000" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4861_dd)">
		<path id="左侧三角-亮" d="M507.32 1465L436.32 1536L507.32 1536L507.32 1465Z" fill="#2194F2" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="左侧三角-亮" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<g filter="url(#filter_43_4862_dd)">
		<path id="左侧三角-亮" d="M330.82 77.5L175.32 77.5L209.82 167L292.82 167L330.82 77.5Z" fill="#2194F2" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="左侧三角-亮" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
</svg>
