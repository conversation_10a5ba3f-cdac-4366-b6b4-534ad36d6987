@import '@/assets/css/helpers.sass'

.box
    position: relative
    :global
        .ant-btn
            background: transparent !important
            border: none
            padding: 0
        .ant-btn>span
            font-size: px(20)
            font-family: "庞门正道标题体"
            color: rgb(139, 180, 226)
        .ant-btn>span:hover
            color: rgb(220, 234, 249)
            text-shadow: 0 0 px(2) rgb(177, 212, 251)
            transform: translateY(px(-2)) // add translateY transform on hover
            background-position: 0 px(3), 0 0
.titleBtn
    height: px(37)
.btn
    border: none
    background: transparent
    font-size: px(20)
    font-family: "庞门正道标题体"
    color: rgb(139, 180, 226)
    border-bottom: 2px solid rgb(139, 180, 226)
.btn:hover
    color: rgb(220, 234, 249)
    text-shadow: 0 0 px(2) rgb(177, 212, 251)
    transform: translateY(px(-2)) // add translateY transform on hover
    background-position: 0 px(3), 0 0
    cursor: pointer

.menu
    border-radius: px(5)
    background: rgba(6, 48, 109,0.9)
ul
    background-color: transparent !important
