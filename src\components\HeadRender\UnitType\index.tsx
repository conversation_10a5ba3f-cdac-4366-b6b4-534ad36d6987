import px from '@/utils/px';
import { useModel } from '@umijs/max';
import { ConfigProvider, Select } from 'antd';
import styles from '../index.sass';

export default function () {
  const {
    initialState: { permission },
  } = useModel('@@initialState');
  const { unitType, setUnitType } = useModel('unitType');
  let temp = permission.unitTypeList;
  if (location.href.includes('/market-trading/market-trading')) {
    temp = permission.unitTypeList.filter((i) => i !== '#1');
    if (unitType === '#1') {
      setUnitType('#2R');
    }
  }
  return (
    <ConfigProvider
      theme={{
        token: {
          /* 这里是你的全局 token */
          fontSize: px(18),
          controlHeight: px(38),
        },
        components: {
          Select: {
            colorBgContainer: 'rgba(0,0,0,0)',
            colorBorder: 'rgb(71, 114, 255)',
            colorText: '#fff',
            colorTextQuaternary: '#fff',
            colorTextPlaceholder: '#fff',
            colorBgElevated: 'rgba(6, 48, 109,1)',
            optionSelectedBg: 'rgb(6, 85, 202)',
          },
        },
      }}
    >
      <img className={styles.userIcon} src="/unittype.png" alt="" />
      <Select
        value={unitType}
        onChange={(v) => {
          setUnitType(v);
        }}
        options={temp.map((item) => {
          return {
            label: item,
            value: item,
          };
        })}
      ></Select>
    </ConfigProvider>
  );
}
