import styles from './index.sass';

interface MenuGroupProps {
  title: string;
  icon: string;
}

const MenuGroup: React.FC<MenuGroupProps> = ({ title, icon }) => {
  return (
    <div className={styles.container}>
      <div
        className={styles.icon}
        style={{ backgroundImage: `url(${icon})` }}
      ></div>
      <div className={styles.title}>{title}</div>
    </div>
  );
};

export default MenuGroup;
