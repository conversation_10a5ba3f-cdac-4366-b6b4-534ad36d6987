<svg width="124.167503" height="149.900391" viewBox="0 0 124.168 149.9" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_19_391_dd" x="25.776070" y="58.732910" width="71.858963" height="25.152832" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="7"/>
			<feGaussianBlur stdDeviation="0"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.027451 0 0 0 0 0.035294 0 0 0 0 0.10196 0 0 0 0.45 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_19_393_dd" x="29.609413" y="46.134766" width="14.269234" height="13.372070" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_394_dd" x="78.019691" y="39.927734" width="14.269234" height="13.372070" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_395_dd" x="61.378609" y="58.677734" width="14.269234" height="13.372070" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_396_dd" x="53.814568" y="62.793945" width="13.512817" height="12.914551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_397_dd" x="50.032494" y="65.080566" width="13.512817" height="12.914551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_398_dd" x="66.673485" y="56.848633" width="13.512817" height="12.914551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_399_dd" x="59.865822" y="66.909668" width="13.512817" height="12.914551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_400_dd" x="40.955559" y="56.848633" width="13.512817" height="12.914551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_401_dd" x="43.981224" y="62.336426" width="13.512817" height="12.914551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_402_dd" x="68.186363" y="49.531738" width="13.512817" height="12.914551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_403_dd" x="41.712029" y="47.702148" width="13.512817" height="12.914551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_404_dd" x="48.519691" y="54.104492" width="14.269234" height="13.372070" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_405_dd" x="62.135094" y="46.787598" width="14.269234" height="13.372070" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_406_dd" x="26.583824" y="29.866699" width="14.269234" height="13.372070" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_407_dd" x="64.404289" y="37.183594" width="14.269234" height="13.372070" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_408_dd" x="34.904274" y="36.726562" width="15.025642" height="13.829102" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_409_dd" x="83.314552" y="30.324219" width="15.025650" height="13.829102" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="2"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_19_410_dd" x="-33.916237" y="-5562.000000" width="0.000000" height="0.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="0.441721" result="effect_layerBlur_1"/>
		</filter>
		<filter id="filter_19_411_dd" x="-33.916237" y="-5562.000000" width="0.000000" height="0.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="0.441721" result="effect_layerBlur_1"/>
		</filter>
		<filter id="filter_19_412_dd" x="-33.916237" y="-5562.000000" width="0.000000" height="0.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="0.441721" result="effect_layerBlur_1"/>
		</filter>
		<filter id="filter_19_413_dd" x="-33.916237" y="-5562.000000" width="0.000000" height="0.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="0.441721" result="effect_layerBlur_1"/>
		</filter>
		<filter id="filter_19_414_dd" x="-33.916237" y="-5562.000000" width="0.000000" height="0.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="0.441721" result="effect_layerBlur_1"/>
		</filter>
		<filter id="filter_19_415_dd" x="-33.916237" y="-5562.000000" width="0.000000" height="0.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="0.441721" result="effect_layerBlur_1"/>
		</filter>
		<linearGradient x1="62.083763" y1="107.666016" x2="62.083763" y2="148.824966" id="paint_linear_19_275_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#70F0FF" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#3DDDFF"/>
		</linearGradient>
		<linearGradient x1="62.083771" y1="114.983398" x2="62.083771" y2="141.508057" id="paint_linear_19_276_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#70F0FF" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#3DDDFF"/>
		</linearGradient>
		<linearGradient x1="84.326485" y1="116.839996" x2="84.326485" y2="102.875381" id="paint_linear_19_281_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DDDFF" stop-opacity="0.603922"/>
			<stop offset="1.000000" stop-color="#2468FF" stop-opacity="0.403922"/>
		</linearGradient>
		<linearGradient x1="39.337147" y1="116.839951" x2="39.337147" y2="102.875748" id="paint_linear_19_282_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DDDFF" stop-opacity="0.603922"/>
			<stop offset="1.000000" stop-color="#2468FF" stop-opacity="0.403922"/>
		</linearGradient>
		<linearGradient x1="61.829029" y1="83.025391" x2="61.829029" y2="114.536346" id="paint_linear_19_283_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DDDFF" stop-opacity="0.603922"/>
			<stop offset="1.000000" stop-color="#2468FF" stop-opacity="0.800000"/>
		</linearGradient>
		<linearGradient x1="61.705547" y1="82.970703" x2="61.705547" y2="114.525902" id="paint_linear_19_285_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#70F0FF" stop-opacity="0.301961"/>
			<stop offset="1.000000" stop-color="#3DDDFF"/>
		</linearGradient>
		<linearGradient x1="21.237621" y1="102.635742" x2="21.237621" y2="104.922348" id="paint_linear_19_288_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF"/>
			<stop offset="1.000000" stop-color="#1968FF"/>
		</linearGradient>
		<linearGradient x1="84.326485" y1="103.120270" x2="84.326485" y2="89.155655" id="paint_linear_19_292_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DDDFF" stop-opacity="0.603922"/>
			<stop offset="1.000000" stop-color="#2468FF" stop-opacity="0.403922"/>
		</linearGradient>
		<linearGradient x1="39.337147" y1="103.120224" x2="39.337147" y2="89.156021" id="paint_linear_19_293_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DDDFF" stop-opacity="0.603922"/>
			<stop offset="1.000000" stop-color="#2468FF" stop-opacity="0.403922"/>
		</linearGradient>
		<linearGradient x1="61.829029" y1="69.305664" x2="61.829029" y2="100.816620" id="paint_linear_19_294_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DDDFF" stop-opacity="0.603922"/>
			<stop offset="1.000000" stop-color="#2468FF" stop-opacity="0.800000"/>
		</linearGradient>
		<linearGradient x1="61.705547" y1="69.250977" x2="61.705547" y2="100.806168" id="paint_linear_19_296_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#70F0FF" stop-opacity="0.301961"/>
			<stop offset="1.000000" stop-color="#3DDDFF"/>
		</linearGradient>
		<linearGradient x1="21.237621" y1="88.916016" x2="21.237621" y2="91.202621" id="paint_linear_19_299_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF"/>
			<stop offset="1.000000" stop-color="#1968FF"/>
		</linearGradient>
		<linearGradient x1="61.829029" y1="61.988770" x2="61.829029" y2="93.499733" id="paint_linear_19_302_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0097AE"/>
			<stop offset="1.000000" stop-color="#001B4E"/>
		</linearGradient>
		<linearGradient x1="84.326477" y1="89.400543" x2="84.326477" y2="75.435928" id="paint_linear_19_303_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DDDFF" stop-opacity="0.603922"/>
			<stop offset="1.000000" stop-color="#2468FF" stop-opacity="0.403922"/>
		</linearGradient>
		<linearGradient x1="39.337147" y1="89.400986" x2="39.337147" y2="75.436783" id="paint_linear_19_304_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DDDFF" stop-opacity="0.603922"/>
			<stop offset="1.000000" stop-color="#2468FF" stop-opacity="0.403922"/>
		</linearGradient>
		<linearGradient x1="61.829029" y1="55.585938" x2="61.829029" y2="87.096901" id="paint_linear_19_305_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DDDFF" stop-opacity="0.603922"/>
			<stop offset="1.000000" stop-color="#2468FF" stop-opacity="0.800000"/>
		</linearGradient>
		<linearGradient x1="50.510365" y1="84.661957" x2="47.711967" y2="87.086365" id="paint_linear_19_310_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF"/>
			<stop offset="1.000000" stop-color="#1968FF"/>
		</linearGradient>
		<linearGradient x1="73.554787" y1="24.635742" x2="73.554787" y2="39.127296" id="paint_linear_19_332_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF" stop-opacity="0.800000"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="72.251129" y1="13.216309" x2="72.251129" y2="34.887573" id="paint_linear_19_340_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF" stop-opacity="0.800000"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="76.336212" y1="16.179688" x2="66.330544" y2="33.433804" id="paint_linear_19_349_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF" stop-opacity="0.800000"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="58.062119" y1="16.586426" x2="58.062119" y2="53.595528" id="paint_linear_19_352_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF" stop-opacity="0.800000"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="62.147045" y1="19.549805" x2="62.147045" y2="53.595436" id="paint_linear_19_360_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="62.147045" y1="19.549805" x2="62.147045" y2="53.595436" id="paint_linear_19_361_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF" stop-opacity="0.800000"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="72.709175" y1="27.111328" x2="72.709175" y2="40.153503" id="paint_linear_19_364_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF" stop-opacity="0.800000"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="71.537621" y1="16.833984" x2="71.537621" y2="36.338158" id="paint_linear_19_371_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF" stop-opacity="0.800000"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="75.213539" y1="19.500977" x2="66.208435" y2="35.029675" id="paint_linear_19_378_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF" stop-opacity="0.800000"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="58.767620" y1="19.866699" x2="58.767620" y2="53.175110" id="paint_linear_19_381_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF" stop-opacity="0.800000"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="62.443275" y1="22.534180" x2="62.443275" y2="53.175251" id="paint_linear_19_389_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="62.443275" y1="22.534180" x2="62.443275" y2="53.175251" id="paint_linear_19_390_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DE6FF" stop-opacity="0.800000"/>
			<stop offset="1.000000" stop-color="#1968FF" stop-opacity="0.600000"/>
		</linearGradient>
		<linearGradient x1="61.705559" y1="83.885605" x2="61.705559" y2="58.732903" id="paint_linear_19_391_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#00BDE4"/>
			<stop offset="1.000000" stop-color="#0046E1"/>
		</linearGradient>
		<linearGradient x1="61.705555" y1="58.732910" x2="61.705555" y2="83.885612" id="paint_linear_19_391_1" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0A0B28"/>
			<stop offset="1.000000" stop-color="#121129" stop-opacity="0.803922"/>
		</linearGradient>
		<linearGradient x1="82.885002" y1="54.159302" x2="80.935005" y2="54.159302" id="paint_linear_19_410_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF" stop-opacity="0.000000"/>
			<stop offset="0.505279" stop-color="#FFFFFF"/>
			<stop offset="1.000000" stop-color="#FFFFFF" stop-opacity="0.000000"/>
		</linearGradient>
		<linearGradient x1="82.885002" y1="54.159302" x2="81.260002" y2="54.159302" id="paint_linear_19_411_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF" stop-opacity="0.000000"/>
			<stop offset="0.505279" stop-color="#FFFFFF"/>
			<stop offset="1.000000" stop-color="#FFFFFF" stop-opacity="0.000000"/>
		</linearGradient>
		<linearGradient x1="25.776085" y1="18.716919" x2="24.151085" y2="18.716919" id="paint_linear_19_412_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF" stop-opacity="0.000000"/>
			<stop offset="0.505279" stop-color="#FFFFFF"/>
			<stop offset="1.000000" stop-color="#FFFFFF" stop-opacity="0.000000"/>
		</linearGradient>
		<linearGradient x1="87.801674" y1="9.113403" x2="86.176674" y2="9.113403" id="paint_linear_19_413_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF" stop-opacity="0.000000"/>
			<stop offset="0.505279" stop-color="#FFFFFF"/>
			<stop offset="1.000000" stop-color="#FFFFFF" stop-opacity="0.000000"/>
		</linearGradient>
		<linearGradient x1="40.904716" y1="63.077271" x2="39.279716" y2="63.077271" id="paint_linear_19_414_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF" stop-opacity="0.000000"/>
			<stop offset="0.505279" stop-color="#FFFFFF"/>
			<stop offset="1.000000" stop-color="#FFFFFF" stop-opacity="0.000000"/>
		</linearGradient>
		<linearGradient x1="62.840385" y1="61.383301" x2="61.215385" y2="61.383301" id="paint_linear_19_415_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF" stop-opacity="0.000000"/>
			<stop offset="0.505279" stop-color="#FFFFFF"/>
			<stop offset="1.000000" stop-color="#FFFFFF" stop-opacity="0.000000"/>
		</linearGradient>
		<linearGradient x1="61.706684" y1="0.000000" x2="61.706684" y2="83.885658" id="paint_linear_19_416_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3DDDFF" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#2468FF" stop-opacity="0.403922"/>
		</linearGradient>
	</defs>
	<path id="Fill 5备份 9" d="M61.89 107.66L3.08 128.24L62.27 148.82L121.08 128.24L61.89 107.66Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 5备份 9" d="M3.08 128.24L62.27 148.82L121.08 128.24L61.89 107.66L3.08 128.24Z" stroke="url(#paint_linear_19_275_0)" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="Fill 5备份 10" d="M61.96 114.98L24.26 128.24L62.2 141.5L99.9 128.24L61.96 114.98Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 5备份 10" d="M24.26 128.24L62.2 141.5L99.9 128.24L61.96 114.98L24.26 128.24Z" stroke="url(#paint_linear_19_276_0)" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="Fill 5备份 16" d="M61.68 89.42L16.83 105.18L61.97 120.93L106.81 105.18L61.68 89.42Z" fill="#1968FF" fill-opacity="0.301961" fill-rule="evenodd"/>
	<path id="Fill 1" d="M61.97 114.53L61.83 120.97L106.67 105.22L106.81 98.78L61.97 114.53Z" fill="url(#paint_linear_19_281_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="Fill 3" d="M16.69 105.22L61.83 120.97L61.97 114.53L16.83 98.78L16.69 105.22Z" fill="url(#paint_linear_19_282_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="Fill 5备份 15" d="M61.68 83.02L16.83 98.78L61.97 114.53L106.81 98.78L61.68 83.02Z" fill="url(#paint_linear_19_283_0)" fill-opacity="0.720000" fill-rule="evenodd"/>
	<path id="Fill 5备份" d="M61.55 82.97L16.69 98.74L61.85 114.52L106.71 98.74L61.55 82.97Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 5备份" d="M16.69 98.74L61.85 114.52L106.71 98.74L61.55 82.97L16.69 98.74Z" stroke="url(#paint_linear_19_285_0)" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="Fill 3备份" d="M16.69 105.1L61.94 120.92L62.08 114.34L16.84 98.51L16.69 105.1Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 3备份" d="M61.94 120.92L62.08 114.34L16.84 98.51L16.69 105.1L61.94 120.92Z" stroke="#3DDDFF" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="Fill 1备份" d="M62.22 114.34L62.08 120.92L106.57 105.1L106.71 98.51L62.22 114.34Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 1备份" d="M62.08 120.92L106.57 105.1L106.71 98.51L62.22 114.34L62.08 120.92Z" stroke="#3DDDFF" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="椭圆形" d="M21.23 104.79C22.07 105.08 22.75 104.86 22.75 104.29C22.75 103.73 22.07 103.04 21.23 102.76C20.4 102.47 19.72 102.69 19.72 103.26C19.72 103.82 20.4 104.51 21.23 104.79Z" fill="url(#paint_linear_19_288_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="椭圆形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 5备份 16" d="M61.68 75.7L16.83 91.46L61.97 107.21L106.81 91.46L61.68 75.7Z" fill="#1968FF" fill-opacity="0.301961" fill-rule="evenodd"/>
	<path id="Fill 1" d="M61.97 100.81L61.83 107.25L106.67 91.5L106.81 85.06L61.97 100.81Z" fill="url(#paint_linear_19_292_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="Fill 3" d="M16.69 91.5L61.83 107.25L61.97 100.81L16.83 85.06L16.69 91.5Z" fill="url(#paint_linear_19_293_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="Fill 5备份 15" d="M61.68 69.3L16.83 85.06L61.97 100.81L106.81 85.06L61.68 69.3Z" fill="url(#paint_linear_19_294_0)" fill-opacity="0.720000" fill-rule="evenodd"/>
	<path id="Fill 5备份" d="M61.55 69.25L16.69 85.02L61.85 100.8L106.71 85.02L61.55 69.25Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 5备份" d="M16.69 85.02L61.85 100.8L106.71 85.02L61.55 69.25L16.69 85.02Z" stroke="url(#paint_linear_19_296_0)" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="Fill 3备份" d="M16.69 91.38L61.94 107.2L62.08 100.62L16.84 84.8L16.69 91.38Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 3备份" d="M61.94 107.2L62.08 100.62L16.84 84.8L16.69 91.38L61.94 107.2Z" stroke="#3DDDFF" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="Fill 1备份" d="M62.22 100.62L62.08 107.2L106.57 91.38L106.71 84.8L62.22 100.62Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 1备份" d="M62.08 107.2L106.57 91.38L106.71 84.8L62.22 100.62L62.08 107.2Z" stroke="#3DDDFF" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="椭圆形" d="M21.23 91.07C22.07 91.36 22.75 91.14 22.75 90.57C22.75 90.01 22.07 89.32 21.23 89.04C20.4 88.75 19.72 88.97 19.72 89.54C19.72 90.1 20.4 90.79 21.23 91.07Z" fill="url(#paint_linear_19_299_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="椭圆形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 1" d="M61.97 87.09L61.83 93.53L106.67 77.78L106.81 71.34L61.97 87.09Z" fill="url(#paint_linear_19_303_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="Fill 3" d="M16.69 77.78L61.83 93.53L61.97 87.09L16.83 71.34L16.69 77.78Z" fill="url(#paint_linear_19_304_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="Fill 5备份 15" d="M61.68 55.58L16.83 71.34L61.97 87.09L106.81 71.34L61.68 55.58Z" fill="url(#paint_linear_19_305_0)" fill-opacity="0.720000" fill-rule="evenodd"/>
	<path id="Fill 5备份" d="M61.55 55.53L16.69 71.31L61.85 87.08L106.71 71.3L61.55 55.53Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 5备份" d="M16.69 71.31L61.85 87.08L106.71 71.3L61.55 55.53L16.69 71.31Z" stroke="#3DDDFF" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="Fill 3备份" d="M16.69 77.66L61.94 93.48L62.08 86.9L16.84 71.08L16.69 77.66Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 3备份" d="M61.94 93.48L62.08 86.9L16.84 71.08L16.69 77.66L61.94 93.48Z" stroke="#3DDDFF" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="Fill 1备份" d="M62.22 86.9L62.08 93.48L106.57 77.66L106.71 71.08L62.22 86.9Z" fill="#FFFFFF" fill-opacity="0" fill-rule="evenodd"/>
	<path id="Fill 1备份" d="M62.08 93.48L106.57 77.66L106.71 71.08L62.22 86.9L62.08 93.48Z" stroke="#3DDDFF" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<mask id="mask_19_310" fill="white">
		<path id="形状结合" d="M48.8886 83.8574L48.8886 87.3057L47.712 86.876L47.712 83.4277L48.8886 83.8574ZM51.242 84.7173L51.242 88.1655L50.0654 87.7358L50.0654 84.2876L51.242 84.7173ZM53.5961 89.0254L53.5961 85.5771L52.4194 85.1475L52.4194 88.5957L53.5961 89.0254ZM55.9488 86.437L55.9488 89.8853L54.7721 89.4556L54.7721 86.0073L55.9488 86.437ZM58.3022 90.7451L58.3022 87.2969L57.1255 86.8667L57.1255 90.3149L58.3022 90.7451Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
	</mask>
	<path id="形状结合" d="M48.8886 83.8574L48.8886 87.3057L47.712 86.876L47.712 83.4277L48.8886 83.8574ZM51.242 84.7173L51.242 88.1655L50.0654 87.7358L50.0654 84.2876L51.242 84.7173ZM53.5961 89.0254L53.5961 85.5771L52.4194 85.1475L52.4194 88.5957L53.5961 89.0254ZM55.9488 86.437L55.9488 89.8853L54.7721 89.4556L54.7721 86.0073L55.9488 86.437ZM58.3022 90.7451L58.3022 87.2969L57.1255 86.8667L57.1255 90.3149L58.3022 90.7451Z" clip-rule="evenodd" fill="url(#paint_linear_19_310_0)" fill-opacity="1.000000" fill-rule="evenodd" mask="url(#mask_19_310)"/>
	<path id="形状结合" d="" clip-rule="evenodd" fill="#979797" fill-opacity="0.000000" fill-rule="evenodd"/>
	<mask id="mask_19_364" fill="white">
		<path id="形状结合" d="M59.0431 34.146L79.023 27.1113L86.3753 29.7783L86.3742 29.7788C86.2423 33.1738 84.6262 36.8091 82.0064 40.1533L82.0073 40.1533L82.0063 40.1533L82.0062 40.1533L81.9504 40.1416L66.3954 36.8618L59.0431 34.146L59.0433 34.1465L59.0431 34.146Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
	</mask>
	<path id="形状结合" d="M59.0431 34.146L79.023 27.1113L86.3753 29.7783L86.3742 29.7788C86.2423 33.1738 84.6262 36.8091 82.0064 40.1533L82.0073 40.1533L82.0063 40.1533L82.0062 40.1533L81.9504 40.1416L66.3954 36.8618L59.0431 34.146L59.0433 34.1465L59.0431 34.146Z" clip-rule="evenodd" fill="url(#paint_linear_19_364_0)" fill-opacity="0.700000" fill-rule="evenodd" mask="url(#mask_19_364)"/>
	<path id="形状结合" d="M84.3698 29.0508L86.3753 29.7783L86.3742 29.7788L86.3741 29.7814C86.2455 33.0728 84.7221 36.5899 82.2417 39.8486C82.1646 39.95 82.0865 40.051 82.0076 40.1518L82.0064 40.1533L81.9504 40.1416L79.7916 39.6864L66.3954 36.8618L62.028 35.2486L59.18 34.1966L59.0433 34.1461L59.0431 34.146L59.0434 34.1459L59.2118 34.0866L62.0443 33.0893L79.023 27.1113L84.3698 29.0508ZM84.1926 31.1475L79.0126 29.2685L65.0292 34.1919L66.9605 34.9053L81.1567 37.8983Q83.6374 34.4123 84.1926 31.1475Z" clip-rule="evenodd" fill="#3DE6FF" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="路径" d="M66.39 36.81L86.37 29.77C86.24 33.17 84.62 36.8 82 40.15L66.39 36.81Z" fill="#3DE6FF" fill-opacity="0.600000" fill-rule="evenodd"/>
	<path id="路径" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<path id="路径备份 15" d="M66.39 36.81L86.37 29.77C86.24 33.17 84.62 36.8 82 40.15L66.39 36.81Z" fill="#D8D8D8" fill-opacity="0" fill-rule="evenodd"/>
	<path id="路径备份 15" d="M84.28 30.51L70.06 35.52L66.39 36.81L70.19 37.62L82 40.15C84.05 37.54 85.48 34.75 86.08 32.03C86.24 31.27 86.34 30.52 86.37 29.77L84.28 30.51ZM81.15 37.89C82.02 36.66 82.72 35.42 83.25 34.18C83.44 33.73 83.61 33.29 83.75 32.85L73.86 36.33L81.15 37.89Z" fill="#3DE6FF" fill-opacity="1.000000" fill-rule="evenodd"/>
	<mask id="mask_19_371" fill="white">
		<path id="形状结合" d="M78.4129 20.0972L71.8606 17.6855L71.84 17.6875C68.3586 16.4043 63.645 16.5229 58.4554 18.3506C58.2585 18.4199 58.0623 18.4912 57.8668 18.5645L57.8671 24.3477L57.8669 24.3477L57.8669 33.5967L57.8675 33.5972L57.8675 33.6709L57.9694 33.6353L65.2198 36.3379L65.2198 36.3384L65.2199 36.3379L85.1997 29.3032C85.2055 29.1533 85.2085 29.0044 85.2085 28.8555C85.2085 24.3921 82.5722 21.3228 78.4129 20.0972Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
	</mask>
	<path id="形状结合" d="M78.4129 20.0972L71.8606 17.6855L71.84 17.6875C68.3586 16.4043 63.645 16.5229 58.4554 18.3506C58.2585 18.4199 58.0623 18.4912 57.8668 18.5645L57.8671 24.3477L57.8669 24.3477L57.8669 33.5967L57.8675 33.5972L57.8675 33.6709L57.9694 33.6353L65.2198 36.3379L65.2198 36.3384L65.2199 36.3379L85.1997 29.3032C85.2055 29.1533 85.2085 29.0044 85.2085 28.8555C85.2085 24.3921 82.5722 21.3228 78.4129 20.0972Z" clip-rule="evenodd" fill="url(#paint_linear_19_371_0)" fill-opacity="0.700000" fill-rule="evenodd" mask="url(#mask_19_371)"/>
	<path id="形状结合" d="M78.4129 20.0972L71.8606 17.6855L71.84 17.6875C68.3586 16.4043 63.645 16.5229 58.4554 18.3506C58.2585 18.4199 58.0623 18.4912 57.8668 18.5645L57.867 22.18L57.8671 24.3477L57.8669 24.3477L57.8669 33.5967L57.8675 33.5972L57.8675 33.6709L57.9694 33.6353L58.065 33.6709L65.2196 36.3378L65.2198 36.3379L67.2511 35.6227L85.1997 29.3032C85.2055 29.1533 85.2085 29.0044 85.2085 28.8555C85.2085 24.3921 82.5722 21.3228 78.4129 20.0972ZM83.1275 27.8793C82.8174 24.9377 81.0544 22.9931 77.8386 22.0454L77.7743 22.0264L77.7114 22.0034L71.5902 19.7504L71.5684 19.7524L71.1373 19.5933C68.0293 18.4477 64.2829 18.5852 59.8981 20.0068L59.8984 27.2715L59.8982 27.2714L59.8982 32.1864L65.2396 34.1778L83.1275 27.8793Z" clip-rule="evenodd" fill="#3DE6FF" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="路径 9" d="" fill="#D8D8D8" fill-opacity="0" fill-rule="nonzero"/>
	<path id="路径 9" d="M58.48 18.92L65.75 21.51" stroke="#3DE6FF" stroke-opacity="1.000000" stroke-width="2.031250"/>
	<path id="路径" d="M65.8 21.01C76.52 17.24 85.2 20.75 85.2 28.85C85.2 29 85.2 29.15 85.19 29.3L65.21 36.33L65.21 21.23C65.41 21.15 65.61 21.08 65.8 21.01Z" fill="url(#paint_linear_19_378_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="路径" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<path id="路径备份 14" d="M65.8 21.01C76.52 17.24 85.2 20.75 85.2 28.85C85.2 29 85.2 29.15 85.19 29.3L65.21 36.33L65.21 21.23C65.41 21.15 65.61 21.08 65.8 21.01Z" fill="#D8D8D8" fill-opacity="0" fill-rule="evenodd"/>
	<path id="路径备份 14" d="M65.8 21.01C76.52 17.24 85.2 20.75 85.2 28.85C85.2 29 85.2 29.15 85.19 29.3L67.25 35.62L65.21 36.33L65.21 34.18L65.21 21.23C65.41 21.15 65.61 21.08 65.8 21.01ZM67.25 22.67L67.25 33.46L83.12 27.88C83.01 26.89 82.72 26.01 82.27 25.24C82.19 25.1 82.1 24.97 82.01 24.84C81.96 24.77 81.91 24.7 81.86 24.63C81.11 23.65 80.08 22.9 78.76 22.36C78.02 22.06 77.21 21.84 76.33 21.7C75.42 21.56 74.45 21.5 73.42 21.54C71.46 21.6 69.4 21.98 67.25 22.67Z" fill="#3DE6FF" fill-opacity="1.000000" fill-rule="evenodd"/>
	<mask id="mask_19_381" fill="white">
		<path id="形状结合" d="M56.692 19.8667L64.0443 22.5337L64.0438 22.5342L64.0442 22.5342L64.0439 36.4233L72.3027 38.3135L72.3042 38.3145L79.655 40.981L79.6395 40.978L79.6557 40.9814C76.0979 45.5225 70.689 49.5259 64.6318 51.6587C59.1709 53.5815 54.237 53.6128 50.7114 52.1089L50.7114 52.1162L44.9934 50.0015C40.6507 48.8408 37.8795 45.73 37.8795 41.1538C37.8795 33.2017 46.2483 23.7808 56.6899 19.8682L56.692 19.8667Z" clip-rule="evenodd" fill="" fill-opacity="1.000000" fill-rule="evenodd"/>
	</mask>
	<path id="形状结合" d="M56.692 19.8667L64.0443 22.5337L64.0438 22.5342L64.0442 22.5342L64.0439 36.4233L72.3027 38.3135L72.3042 38.3145L79.655 40.981L79.6395 40.978L79.6557 40.9814C76.0979 45.5225 70.689 49.5259 64.6318 51.6587C59.1709 53.5815 54.237 53.6128 50.7114 52.1089L50.7114 52.1162L44.9934 50.0015C40.6507 48.8408 37.8795 45.73 37.8795 41.1538C37.8795 33.2017 46.2483 23.7808 56.6899 19.8682L56.692 19.8667Z" clip-rule="evenodd" fill="url(#paint_linear_19_381_0)" fill-opacity="0.700000" fill-rule="evenodd" mask="url(#mask_19_381)"/>
	<path id="形状结合" d="M63.6281 22.3827L56.692 19.8667L56.6899 19.8682C46.2483 23.7808 37.8795 33.2017 37.8795 41.1538C37.8795 45.73 40.6507 48.8408 44.9934 50.0015L48.6801 51.365L50.7087 52.1152L50.7114 52.1162L50.7114 52.1089L50.7137 52.1099C51.3464 52.3796 52.0244 52.5999 52.7426 52.7687C56.0266 53.5405 60.1519 53.2361 64.6318 51.6587C69.9421 49.7889 74.754 46.4814 78.2573 42.6344C78.7414 42.1028 79.2004 41.561 79.6324 41.0111C79.6396 41.002 79.6468 40.9928 79.654 40.9837L79.6557 40.9814L79.6548 40.9813L79.6395 40.978L79.655 40.981L79.64 40.9755L77.6394 40.2498L72.3042 38.3145L72.3027 38.3135L64.0439 36.4233L64.0442 23.1404L64.0442 22.5347L64.0442 22.5342L64.0438 22.5342L64.0443 22.5337L64.0435 22.5334L63.6281 22.3827ZM62.0129 23.9578L56.7154 22.0361Q52.9894 23.5258 49.8489 25.8178Q47.1539 27.7846 44.8901 30.3423C41.5706 34.0928 39.9108 37.6963 39.9108 41.1538C39.9108 44.7446 41.7799 47.04 45.518 48.0396L45.6093 48.064L48.6801 49.1994L48.6801 49.0337L51.5085 50.2402C54.8349 51.6597 58.9845 51.4937 63.9572 49.7427C68.7804 48.0443 72.8614 45.4262 76.2001 41.8882L71.73 40.2666L62.0126 38.0425L62.0129 23.9578Z" clip-rule="evenodd" fill="#3DE6FF" fill-opacity="1.000000" fill-rule="evenodd"/>
	<g opacity="0.799944">
		<path id="路径" d="M64.04 22.53L64.04 37.64L79.65 40.98C76.09 45.52 70.68 49.52 64.63 51.65C53.91 55.43 45.23 51.92 45.23 43.82C45.23 35.86 53.6 26.44 64.04 22.53Z" fill="url(#paint_linear_19_389_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="路径备份 16" d="M64.04 22.53L64.04 37.64L79.65 40.98C76.09 45.52 70.68 49.52 64.63 51.65C53.91 55.43 45.23 51.92 45.23 43.82C45.23 35.86 53.6 26.44 64.04 22.53Z" fill="url(#paint_linear_19_390_0)" fill-opacity="0" fill-rule="evenodd"/>
	<path id="路径备份 16" d="M64.04 37.64L64.04 24.71L64.04 22.53C63.35 22.79 62.67 23.07 62.01 23.37C52.53 27.67 45.23 36.39 45.23 43.82C45.23 51.92 53.91 55.43 64.63 51.65C69.89 49.8 74.66 46.54 78.16 42.73C78.68 42.16 79.18 41.57 79.65 40.98L77.42 40.5L64.04 37.64ZM75.85 42.24L63.61 39.62L62.01 39.28L62.01 25.6C60.3 26.42 58.7 27.38 57.2 28.48C55.4 29.79 53.75 31.3 52.24 33C48.92 36.75 47.26 40.36 47.26 43.82C47.26 45.36 47.61 46.66 48.32 47.73C49.06 48.84 50.17 49.7 51.67 50.3C54.98 51.64 59.08 51.45 63.95 49.74C67.44 48.51 70.53 46.81 73.24 44.62C74.16 43.88 75.03 43.09 75.85 42.24Z" fill="#3DE6FF" fill-opacity="1.000000" fill-rule="evenodd"/>
	<g filter="url(#filter_19_391_dd)">
		<path id="Fill 5备份 17" d="M61.58 58.73L25.77 71.3L61.82 83.88L97.63 71.3L61.58 58.73Z" fill="url(#paint_linear_19_391_0)" fill-opacity="0" fill-rule="evenodd"/>
		<path id="Fill 5备份 17" d="M61.58 58.73L25.77 71.3L61.82 83.88L97.63 71.3L61.58 58.73Z" fill="url(#paint_linear_19_391_1)" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_19_393_dd)">
		<ellipse id="椭圆形" cx="36.744026" cy="52.820801" rx="1.134616" ry="0.685983" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形" cx="36.744026" cy="52.820801" rx="0.634616" ry="0.185983" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_394_dd)">
		<ellipse id="椭圆形备份 28" cx="85.154305" cy="46.613770" rx="1.134616" ry="0.685983" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 28" cx="85.154305" cy="46.613770" rx="0.634616" ry="0.185983" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_395_dd)">
		<ellipse id="椭圆形备份 29" cx="68.513222" cy="65.363770" rx="1.134616" ry="0.685983" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 29" cx="68.513222" cy="65.363770" rx="0.634616" ry="0.185983" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_396_dd)">
		<ellipse id="椭圆形备份 34" cx="60.570976" cy="69.251465" rx="0.756410" ry="0.457322" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 34" cx="60.570976" cy="69.251465" rx="0.256410" ry="0.000000" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_397_dd)">
		<ellipse id="椭圆形备份 43" cx="56.788902" cy="71.538086" rx="0.756410" ry="0.457322" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 43" cx="56.788902" cy="71.538086" rx="0.256410" ry="0.000000" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_398_dd)">
		<ellipse id="椭圆形备份 35" cx="73.429893" cy="63.306152" rx="0.756410" ry="0.457322" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 35" cx="73.429893" cy="63.306152" rx="0.256410" ry="0.000000" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_399_dd)">
		<ellipse id="椭圆形备份 36" cx="66.622231" cy="73.367188" rx="0.756410" ry="0.457322" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 36" cx="66.622231" cy="73.367188" rx="0.256410" ry="0.000000" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_400_dd)">
		<ellipse id="椭圆形备份 37" cx="47.711967" cy="63.306152" rx="0.756410" ry="0.457322" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 37" cx="47.711967" cy="63.306152" rx="0.256410" ry="0.000000" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_401_dd)">
		<ellipse id="椭圆形备份 38" cx="50.737633" cy="68.793945" rx="0.756410" ry="0.457322" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 38" cx="50.737633" cy="68.793945" rx="0.256410" ry="0.000000" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_402_dd)">
		<ellipse id="椭圆形备份 39" cx="74.942772" cy="55.989258" rx="0.756410" ry="0.457322" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 39" cx="74.942772" cy="55.989258" rx="0.256410" ry="0.000000" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_403_dd)">
		<ellipse id="椭圆形备份 40" cx="48.468437" cy="54.159668" rx="0.756410" ry="0.457322" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 40" cx="48.468437" cy="54.159668" rx="0.256410" ry="0.000000" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_404_dd)">
		<ellipse id="椭圆形备份 30" cx="55.654305" cy="60.790527" rx="1.134616" ry="0.685983" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 30" cx="55.654305" cy="60.790527" rx="0.634616" ry="0.185983" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_405_dd)">
		<ellipse id="椭圆形备份 41" cx="69.269707" cy="53.473633" rx="1.134616" ry="0.685983" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 41" cx="69.269707" cy="53.473633" rx="0.634616" ry="0.185983" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_406_dd)">
		<ellipse id="椭圆形备份 42" cx="33.718437" cy="36.552734" rx="1.134616" ry="0.685983" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 42" cx="33.718437" cy="36.552734" rx="0.634616" ry="0.185983" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_407_dd)">
		<ellipse id="椭圆形备份 31" cx="71.538902" cy="43.869629" rx="1.134616" ry="0.685983" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 31" cx="71.538902" cy="43.869629" rx="0.634616" ry="0.185983" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_408_dd)">
		<ellipse id="椭圆形备份 32" cx="42.417091" cy="43.641113" rx="1.512821" ry="0.914643" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 32" cx="42.417091" cy="43.641113" rx="1.012821" ry="0.414643" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<g filter="url(#filter_19_409_dd)">
		<ellipse id="椭圆形备份 33" cx="90.827370" cy="37.238770" rx="1.512821" ry="0.914643" fill="#FFFFFF" fill-opacity="1.000000"/>
		<ellipse id="椭圆形备份 33" cx="90.827370" cy="37.238770" rx="1.012821" ry="0.414643" stroke="#979797" stroke-opacity="0" stroke-width="1.000000"/>
	</g>
	<path id="形状结合" d="M97.6362 0L25.7772 0L25.7772 71.3423L25.8703 71.3423L61.8235 83.8857L97.5422 71.3423L97.6362 71.3423L97.6362 0Z" clip-rule="evenodd" fill="url(#paint_linear_19_416_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
</svg>
