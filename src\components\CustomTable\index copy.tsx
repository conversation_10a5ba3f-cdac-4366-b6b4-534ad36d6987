import px from '@/utils/px';
import { ConfigProvider, Table, TableProps } from 'antd'; // 注意这里导入了TableProps类型
import React from 'react';
import styles from './index.sass';

const CustomTable: React.FC<TableProps<any>> = (props) => {
  return (
    <ConfigProvider
      theme={{
        token: {
          /* 这里是你的全局 token */
          fontSize: px(18),
          controlHeight: px(35),
        },
        components: {
          Table: {
            headerColor: 'white',
            colorText: 'white',
            padding: px(5),
            paddingXS: px(5),
            colorBgContainer: '#001A3F',
            headerBg: '#001A3F',
            lineType: 'dashed',
            cellPaddingBlock: px(15),
            cellPaddingBlockSM: px(10),
            borderColor: '#2080fd',
            headerSplitColor: 'rgba(0,0,0,0)',
            bodySortBg: 'white',
          },
        },
      }}
    >
      <Table {...props} className={`${styles.table} ${props.className}`} />
    </ConfigProvider>
  );
};

export default CustomTable;
