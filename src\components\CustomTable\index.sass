@import '@/assets/css/helpers.sass'
.table
    :global
        .ant-table-measure-row,
        .ant-pagination-options
            display: none
        .ant-table-thead >tr>th
            color: white
            border-bottom: px(1) #2080fd dashed
            font-size: px(18)
            font-weight: bolder
            text-align: center
            padding: px(24) px(5)
        .ant-table-tbody >tr >td
            color: white
            border-bottom: px(1) #2080fd dashed
            font-size: px(16)
            color: #20dbfd
            text-align: center
            font-weight: 500
            padding: px(18) px(5)
        .ant-table-small td, .ant-table-small th
            padding: px(10) !important
        .ant-table-column-sorter,.ant-table-column-sorters:hover .ant-table-column-sorter
            color: white
        .ant-table,.ant-table-thead >tr>th,.ant-table-thead th.ant-table-column-has-sorters:hover,
        .ant-table-thead th.ant-table-column-sort,.ant-table-thead th.ant-table-column-sort:hover
            background: rgba(6, 48, 109, 50%)
            color: white
        td.ant-table-column-sort
            background: rgba(6, 48, 109, 0)
        .ant-table-tbody >tr.ant-table-row:hover>td,.ant-table-tbody >tr >td.ant-table-cell-row-hover
            background: url('@/assets/bg/markettrading/bg5.png')
            background-size: 100% 100%
        .ant-table-thead >tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before
            width: 0
        .ant-table-cell-fix-right,.ant-table-cell-fix-left,.ant-table-cell-fix-left,.ant-table-cell-fix-right
            background: rgba(6, 48, 109, 1) !important
            color: white

        .ant-pagination,.ant-pagination-jump-next,.ant-pagination-prev,.ant-pagination-next
            color: white
            font-size: px(16)
        .ant-pagination-next, .ant-pagination-prev,.ant-pagination-item,
        .ant-pagination-item-link,.ant-pagination-jump-next,.ant-pagination-jump-prev,.ant-pagination-item-active
            background-color: rgba(6, 48, 109, 0)
            background: rgba(6, 48, 109, 0)
            height: px(30)
            line-height: px(30)
        .ant-pagination-item:hover
            background: rgba(6, 48, 109, 0) !important
            border: px(2) solid #2080fd
            color: white
        .ant-pagination.ant-pagination-mini .ant-pagination-prev,
        .ant-pagination.ant-pagination-mini .ant-pagination-item,
        .ant-pagination.ant-pagination-mini .ant-pagination-next,
        .ant-pagination.ant-pagination-mini .ant-pagination-jump-prev,
        .ant-pagination.ant-pagination-mini .ant-pagination-jump-next,
        .ant-pagination.ant-pagination-mini .ant-pagination-item-active
            height: px(30)
            line-height: px(30)
            width: px(38)
            margin: px(2)
            color: white
        .ant-pagination .ant-pagination-item a
            background: rgba(6, 48, 109, 0)
            border-radius: px(5)
            font-size: px(16)
            width: px(38)
            height: px(30)
            line-height: px(30)
            color: white
        .ant-pagination .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis,
        .ant-pagination .ant-pagination-disabled .ant-pagination-item-link,
        .ant-pagination .ant-pagination-prev .ant-pagination-item-link,
        .ant-pagination .ant-pagination-next .ant-pagination-item-link,
        .ant-pagination .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis
            color: white
            font-size: px(16)
        .ant-table-content::-webkit-scrollbar
            width: px(10)
            height: px(14)
            border-radius: px(10)
            background-color: rgba(196, 196, 196,0.5 )
        .ant-table-content::-webkit-scrollbar-thumb
            background-color: #47d3e7
            border-radius: px(10)
