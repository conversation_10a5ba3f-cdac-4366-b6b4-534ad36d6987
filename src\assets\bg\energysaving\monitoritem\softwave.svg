<svg width="87.000000" height="83.000000" viewBox="0 0 87 83" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_63_1088_dd" x="-5590.000000" y="-108.000000" width="0.000000" height="0.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="4"/>
			<feGaussianBlur stdDeviation="3.33333"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<filter id="filter_63_1091_dd" x="1.000000" y="49.454544" width="86.000000" height="33.545456" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="4"/>
			<feGaussianBlur stdDeviation="3.33333"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
			<feBlend mode="normal" in2="shape" result="effect_innerShadow_1"/>
		</filter>
		<clipPath id="clip63_1085">
			<rect id="进程" width="36.000000" height="36.000000" transform="translate(27.963379 23.000000)" fill="white" fill-opacity="0"/>
		</clipPath>
		<linearGradient id="paint_linear_63_1087_0" x1="91.107841" y1="52.294651" x2="91.107841" y2="0.000000" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0077FF" stop-opacity="0.301961"/>
			<stop offset="0.572145" stop-color="#4593EC" stop-opacity="0.031373"/>
			<stop offset="1.000000" stop-color="#85BCFB" stop-opacity="0.000000"/>
		</linearGradient>
		<radialGradient id="paint_Diamond_63_1088_0" cx="0.000000" cy="0.000000" r="1.000000" gradientUnits="userSpaceOnUse" gradientTransform="translate(43 65.2273) rotate(0) scale(43 110.239)">
			<stop stop-color="#0077FF"/>
			<stop stop-color="#0077FF" stop-opacity="0.027451"/>
			<stop offset="1.000000" stop-color="#0077FF"/>
		</radialGradient>
		<linearGradient id="paint_linear_63_1090_0" x1="92.107841" y1="53.294651" x2="92.107841" y2="1.000000" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0077FF" stop-opacity="0.301961"/>
			<stop offset="0.572145" stop-color="#4593EC" stop-opacity="0.031373"/>
			<stop offset="1.000000" stop-color="#85BCFB" stop-opacity="0.000000"/>
		</linearGradient>
		<radialGradient id="paint_Diamond_63_1091_0" cx="0.000000" cy="0.000000" r="1.000000" gradientUnits="userSpaceOnUse" gradientTransform="translate(44 66.2273) rotate(0) scale(43 110.239)">
			<stop stop-color="#0077FF"/>
			<stop stop-color="#0077FF" stop-opacity="0.027451"/>
			<stop offset="1.000000" stop-color="#66CFFF"/>
		</radialGradient>
	</defs>
	<rect id="组件 10" width="87.000000" height="83.000000" fill="#FFFFFF" fill-opacity="0"/>
	<rect id="实例 16" width="86.000000" height="82.000000" fill="#FFFFFF" fill-opacity="0"/>
	<rect id="组件 6" width="86.000000" height="82.000000" transform="translate(1.000000 1.000000)" fill="#FFFFFF" fill-opacity="0"/>
	<path id="矩形" d="M1 1L87 1L87 66.2273C87 66.2273 83.9766 58.8283 81.873 56.9091C79.7588 54.9797 76.5493 55.5786 74.5835 54.6978C65 50.4045 49.9492 49.4545 44 49.4545C36.9082 49.4545 24.2085 49.4545 11.5688 54.6978C9.90332 55.3887 5.33691 56.5627 3.37695 58.8109C1.76611 60.6586 1 66.2273 1 66.2273L1 1Z" fill="url(#paint_linear_63_1090_0)" fill-opacity="1.000000" fill-rule="nonzero"/>
	<path id="矩形" d="M87 1L87 66.2273C87 66.2273 83.9766 58.8283 81.873 56.9091C79.7588 54.9797 76.5493 55.5786 74.5835 54.6978C65 50.4045 49.9492 49.4545 44 49.4545C36.9082 49.4545 24.2085 49.4545 11.5688 54.6978C9.90332 55.3887 5.33691 56.5627 3.37695 58.8109C1.76611 60.6586 1 66.2273 1 66.2273L1 1L87 1Z" stroke="#979797" stroke-opacity="0" stroke-width="0.000000"/>
	<g filter="url(#filter_63_1091_dd)">
		<ellipse id="椭圆 1" cx="44.000000" cy="66.227264" rx="43.000000" ry="16.772728" fill="url(#paint_Diamond_63_1091_0)" fill-opacity="1.000000"/>
	</g>
	<g clip-path="url(#clip63_1085)">
		<path id="path" d="M29.4634 40.6725L33.2012 40.6725C35.2095 40.6725 36.9756 39.2425 37.5273 37.1705L39.9072 28.2385C40.0913 27.5385 40.7715 27.1345 41.4214 27.3345C41.5312 27.3703 41.6343 27.42 41.731 27.4836C41.8271 27.5473 41.9136 27.6226 41.9893 27.7095C42.0654 27.7965 42.1284 27.892 42.1787 27.996C42.229 28.1 42.2646 28.2089 42.2852 28.3225L46.7095 50.4765C47.1973 52.9265 49.4453 54.4865 51.7275 53.9605C53.2534 53.6105 54.4775 52.3825 54.9072 50.7725L57.2832 41.8405C57.4673 41.1485 58.0576 40.6725 58.7256 40.6725L62.4634 40.6725C63.2915 40.6725 63.9634 39.9525 63.9634 39.0625C63.9634 38.1745 63.2915 37.4525 62.4634 37.4525L58.7256 37.4525C56.7173 37.4525 54.9512 38.8825 54.3994 40.9545L52.0195 49.8885C51.9917 50 51.9497 50.1058 51.8936 50.2059C51.8374 50.3059 51.7686 50.3967 51.6875 50.478C51.6064 50.5594 51.5161 50.6285 51.416 50.6853C51.3164 50.7421 51.2109 50.7845 51.0996 50.8125C50.4355 50.9645 49.7832 50.5125 49.6416 49.8025L45.2173 27.6485C44.8916 26.0085 43.7476 24.6985 42.2476 24.2385C40.0015 23.5505 37.6636 24.9425 37.0215 27.3525L34.6416 36.2865C34.4595 36.9765 33.8691 37.4525 33.2012 37.4525L29.4634 37.4525C28.6353 37.4525 27.9634 38.1725 27.9634 39.0625C27.9634 39.9525 28.6353 40.6725 29.4634 40.6725Z" fill="#2AA3E0" fill-opacity="1.000000" fill-rule="nonzero"/>
	</g>
</svg>
