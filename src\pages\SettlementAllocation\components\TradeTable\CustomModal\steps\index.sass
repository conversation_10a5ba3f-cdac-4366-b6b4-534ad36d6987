@import '~@/assets/css/helpers.sass'
.stepContent
  margin-top: px(20)
  padding: px(20)
  background: linear-gradient(135deg, rgba(12, 67, 126, 0.2), rgba(29, 92, 158, 0.1))
  border-radius: px(8)
  border: 1px solid rgba(71, 114, 255, 0.2)
  min-height: px(300)
  width: 100%
  overflow-x: auto

.stepTitle
  color: #94EFFF
  font-size: px(20)
  font-weight: 600
  margin-bottom: px(20)
  text-shadow: 0 0 px(10) rgba(148, 239, 255, 0.5)
  border-bottom: 2px solid rgba(71, 114, 255, 0.3)
  padding-bottom: px(10)

.table
  width: px(1500)
  :global
    .ant-table-thead >tr>th
      color: white
      border-bottom: px(1) #2080fd dashed
      font-size: px(18)
      font-weight: bolder
      text-align: center
      padding: px(24) px(5)
    .ant-table-tbody >tr >td
      color: white
      border-bottom: px(1) #2080fd dashed
      font-size: px(16)
      color: #20dbfd
      text-align: center
      font-weight: 500
      padding: px(18) px(5) 
    .ant-table-column-sorter,.ant-table-column-sorters:hover .ant-table-column-sorter
        color: white
    .ant-table,.ant-table-thead >tr>th,.ant-table-thead th.ant-table-column-has-sorters:hover,
    .ant-table-thead th.ant-table-column-sort,.ant-table-thead th.ant-table-column-sort:hover
        background: rgba(6, 48, 109, 50%)
        color: white
    td.ant-table-column-sort
        background: rgba(6, 48, 109, 0)
    .ant-table-tbody >tr.ant-table-row:hover>td,.ant-table-tbody >tr >td.ant-table-cell-row-hover
        background: url('@/assets/bg/markettrading/bg5.png')
        background-size: 100% 100%
    .ant-table-thead >tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before
        width: 0
    .ant-table-cell-scrollbar:not([rowspan])
      box-shadow: none !important
    .ant-table-cell-fix-right,.ant-table-cell-fix-left,.ant-table-cell-fix-left,.ant-table-cell-fix-right
      background: rgba(6, 48, 109, 1) !important
      color: white
