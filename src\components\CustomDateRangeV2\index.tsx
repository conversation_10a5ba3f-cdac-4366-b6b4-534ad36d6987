import px from '@/utils/px';
import {
  dateFormat,
  datetimeFormat,
  defalutTime,
  myLocale,
  rangePresets,
} from '@/utils/util';
import { DatePicker } from 'antd';
import { Dayjs } from 'dayjs';
import styles from './index.sass';
interface IProp {
  onChange?: () => void;
  defaultTimeRange?: [Dayjs, Dayjs];
  showTime?: boolean;
}
export default function (props: IProp) {
  const { onChange, defaultTimeRange, showTime } = props;
  return (
    <DatePicker.RangePicker
      allowClear={false}
      showTime={showTime || false}
      locale={myLocale}
      presets={rangePresets}
      format={showTime ? datetimeFormat : dateFormat}
      defaultValue={defaultTimeRange || defalutTime}
      onChange={onChange}
      className={styles.box}
      style={{
        width: showTime ? px(350) : px(230),
      }}
    />
  );
}
