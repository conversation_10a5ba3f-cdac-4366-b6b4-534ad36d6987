import CustomButton from '@/components/CustomButton';
import CustomModal from '@/components/CustomModal';
import { AGenerateRecallParamsEfile } from '@/services/resourceMonitor';
import px from '@/utils/px';
import { DatePicker, message } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useState } from 'react';
import styles from './index.sass';

interface TimeRangeModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (startDate: string, endDate: string) => void;
}

const TimeRangeModal: React.FC<TimeRangeModalProps> = ({
  open,
  onCancel,
  onConfirm,
}) => {
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null]>([
    null,
    null,
  ]);
  const [loading, setLoading] = useState(false);

  const handleDateRangeChange = (dates: null | (Dayjs | null)[]) => {
    setDateRange(dates as [Dayjs | null, Dayjs | null]);
  };

  const handleConfirm = () => {
    if (dateRange[0] && dateRange[1]) {
      setLoading(true);
      const startDay = dateRange[0].format('YYYY-MM-DD');
      const endDay = dateRange[1].format('YYYY-MM-DD');

      AGenerateRecallParamsEfile({
        startDay,
        endDay,
      })
        .then(() => {
          message.success('历史数据获取成功！');
          onConfirm(startDay, endDay);
          onCancel();
          setLoading(false);
        })
        .catch(() => {
          message.error('历史数据获取失败！');
          setLoading(false);
        });
    }
  };

  return (
    <CustomModal
      open={open}
      title="选择时间范围"
      onCancel={onCancel}
      footer={null}
      className={styles.modal}
      width={px(500)}
      centered={true}
    >
      <div className={styles.content}>
        <div className={styles.dateRangeContainer}>
          <DatePicker.RangePicker
            showTime={false}
            format="YYYY-MM-DD"
            placeholder={['开始时间', '结束时间']}
            value={dateRange}
            onChange={handleDateRangeChange}
            className={styles.dateRange}
          />
        </div>
        <div className={styles.footer}>
          <CustomButton
            className={styles.confirmButton}
            onClick={handleConfirm}
            disabled={!dateRange[0] || !dateRange[1]}
            loading={loading}
          >
            确认
          </CustomButton>
        </div>
      </div>
    </CustomModal>
  );
};

export default TimeRangeModal;
