import { INameList } from '@/pages/MarketTrading/typing';
import { mockDateTable } from '@/pages/SettlementAllocation/components/DateTable/mock';
import { mockIncomeTable } from '@/pages/SettlementAllocation/components/IncomeTable/mock';
import { mockPriceTable } from '@/pages/SettlementAllocation/components/PriceTable/mock';
import {
  data as resourceTable,
  timeData as resourceTimeTable,
} from '@/pages/SettlementAllocation/components/ResourceTable/mock';
import { mockSumTable } from '@/pages/SettlementAllocation/components/SumTable/mock';
import { data as detail } from '@/pages/SettlementAllocation/components/TradeTable/CustomModal/mock';
import { data } from '@/pages/SettlementAllocation/components/TradeTable/mock';
import { statisticChart } from '@/pages/SettlementAllocation/mock';

import {
  IBenefitDetailList,
  IBenefitList,
  IStatisticData,
  TRADE_TYPE,
} from '@/pages/SettlementAllocation/typing';
import { Response } from '@/typing';
import { mock } from '@/utils/util';
import axios from 'axios';

const api = 'settlementAllocation';
const getTradeUrl = (tradeType: TRADE_TYPE) => {
  if (tradeType === TRADE_TYPE.daytrade) {
    return 'profit';
  } else if (tradeType === TRADE_TYPE.demand) {
    return 'demandResponse';
  } else {
    return 'ancillaryService';
  }
};
export const AAddBenefitData = (params: {
  cost: number;
  income: number;
  name: string;
  profitId: number;
  profitValue: number;
  time: string;
  winbidId: number;
}): Promise<boolean> => {
  if (mock) {
    return new Promise((resolve) => {
      axios
        .post<Response<boolean>>('/api/profitDistribution/profit', {
          params,
        })
        .then(() => {
          resolve(true);
        });
    });
  }
  return new Promise((resolve) => {
    axios
      .post<Response<boolean>>('/' + api + '/profitDistribution/profit', params)
      .then(() => {
        resolve(true);
      });
  });
};
export const AUpdateBenefitData = (params: {
  profitId: string;
  cost: number;
  income: number;
  profitValue: number;
  startTime: string;
  endTime: string;
}): Promise<boolean> => {
  console.log(params);
  if (mock) {
    return new Promise((resolve) => {
      axios
        .post<Response<boolean>>('/api/profitDistribution/profit', {
          params,
        })
        .then(() => {
          resolve(true);
        });
    });
  }
  return new Promise((resolve) => {
    axios
      .put<Response<boolean>>(
        '/' + api + '/profitDistribution/profit/' + params.profitId,
        params,
      )
      .then(() => {
        resolve(true);
      });
  });
};
export const ADeleteTradeData = (params: {
  profitId: number;
}): Promise<boolean> => {
  return new Promise((resolve) => {
    axios
      .delete('/' + api + '/profitDistribution/profit/' + params.profitId)
      .then(() => {
        resolve(true);
      });
  });
};
export const AGetBenefitList = (params?: {
  endTime?: string;
  name?: string;
  startTime?: string;
  decisionStatus?: string;
  current: number;
  pageSize: number;
  tradeType: TRADE_TYPE;
}): Promise<IBenefitList> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: data,
      });
    });
  }

  return new Promise((resolve) => {
    axios
      .get<Response<IBenefitList>>(
        '/' +
          api +
          '/profitDistribution/' +
          getTradeUrl(params.tradeType) +
          '/page',
        {
          params,
        },
      )
      .then((res) => {
        resolve(res.data.data);
      });
  });
};
export const AGetDailyBenefitList = (params?: {
  current: number;
  pageSize: number;
}): Promise<IBenefitList> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: data,
      });
    });
  }

  return new Promise((resolve) => {
    axios
      .get<Response<IBenefitList>>(
        '/' + api + '/profitDistribution/secondRController/getReportList',
        { params },
      )
      .then((res) => {
        resolve(res.data.data);
      });
  });
};

export const AGetDailyProgress = (params?: {
  dayStr: string;
}): Promise<IBenefitList> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: data,
      });
    });
  }

  return new Promise((resolve) => {
    axios
      .get<Response<IBenefitList>>(
        '/' + api + '/profitDistribution/secondRController/getOneDailyProgress',
        { params },
      )
      .then((res) => {
        resolve(res.data.data);
      });
  });
};

export const AGetNameList = (): Promise<INameList[]> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve([
        {
          winbidId: 1,
          name: '采购需求1',
        },
        {
          winbidId: 2,
          name: '采购需求1',
        },
        {
          winbidId: 1,
          name: '采购需求1',
        },
        {
          winbidId: 3,
          name: '采购需求1',
        },
      ]);
    });
  }
  return new Promise((resolve) => {
    axios
      .get<Response<{ allWinbidRecordsNameList: INameList[] }>>(
        '/' + api + '/profitDistribution/profit/getAllWinbidRecordsNameList',
      )
      .then((res) => {
        resolve(res.data.data.allWinbidRecordsNameList);
      });
  });
};
export const AGetBenefitDetailList = (params?: {
  pageSize: number;
  current: number;
  profitId: number;
  tradeType: TRADE_TYPE;
}): Promise<IBenefitDetailList> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: detail,
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get<Response<IBenefitDetailList>>(
        '/' +
          api +
          '/profitDistribution/' +
          getTradeUrl(params.tradeType) +
          '/pageAllResourceProfitByProfitId/' +
          params?.profitId,
        { params },
      )
      .then((res) => {
        resolve(res.data.data);
      });
  });
};

export const AGetResourceTable = (params?: {
  resourceName?: string;
  resourceType?: string;
  current: number;
  pageSize: number;
  tradeType: TRADE_TYPE;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: resourceTable,
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get<Response<IBenefitList>>(
        '/' +
          api +
          '/profitDistribution/' +
          getTradeUrl(params.tradeType) +
          '/pageResourceSummaryProfit',
        {
          params,
        },
      )
      .then((res) => {
        resolve(res.data.data);
      });
  });
};

export const AGetResourceTimeTable = (params?: {
  tradeType: TRADE_TYPE;
  resourceId: string;
  current: number;
  pageSize: number;
  startTimeString?: string;
  endTimeString?: string;
  periodTypeString?: string;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: resourceTimeTable,
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get<Response<IBenefitList>>(
        '/' +
          api +
          '/profitDistribution/' +
          getTradeUrl(params.tradeType) +
          '/pageResourceDayOrMonthSummaryProfit/' +
          params?.resourceId,
        {
          params,
        },
      )
      .then((res) => {
        resolve(res.data.data);
      });
  });
};

export const AGetUserTable = (params?: {
  resourceUserName?: string;
  current: number;
  pageSize: number;
  tradeType: TRADE_TYPE;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: resourceTable,
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get<Response<IBenefitList>>(
        '/' +
          api +
          '/profitDistribution/' +
          getTradeUrl(params.tradeType) +
          '/pageResourceUserSummaryProfit',
        {
          params,
        },
      )
      .then((res) => {
        resolve(res.data.data);
      });
  });
};

export const AGetUserDetailTimeTable = (params?: {
  tradeType: TRADE_TYPE;
  resourceUserId: string;
  current: number;
  pageSize: number;
  startTimeString?: string;
  endTimeString?: string;
  periodTypeString?: string;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: resourceTimeTable,
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get<Response<IBenefitList>>(
        '/' +
          api +
          '/profitDistribution/' +
          getTradeUrl(params.tradeType) +
          '/pageResourceUserDayOrMonthSummaryProfit/' +
          params?.resourceUserId,
        {
          params,
        },
      )
      .then((res) => {
        resolve(res.data.data);
      });
  });
};
export const AGetStatisticChart = (): Promise<Response<IStatisticData>> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: statisticChart });
    });
  }
  return new Promise((resolve) => {
    axios
      .get<Response<IStatisticData>>(
        '/' + api + '/profitDistribution/profit/resourceUserTotalProfitRank',
      )
      .then((res) => {
        resolve(res.data);
      });
  });
};
export const AGetResourceProfit = (params: { name: string }) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          historyProfitCurve: [
            {
              time: '2024-01-12',
              totalProfit: 84.6,
            },
            {
              time: '2024-01-13',
              totalProfit: 79.87,
            },
          ],
        },
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get<Response<IStatisticData>>(
        '/' +
          api +
          '/profitDistribution/profit/getResourceUserHistoryProfitCurve',
        {
          params,
        },
      )
      .then((res) => {
        resolve(res.data);
      });
  });
};
export const ADownLoadExcel = (params: {
  type: '交易收益' | '资源收益' | '用户收益' | '交易详情收益';
}) => {
  let url = '';
  if (params.type === '交易收益') {
    url = '/profitDistribution/profit/getTransactionExcel';
  } else if (params.type === '资源收益') {
    url = '/profitDistribution/profit/getResourceExcel';
  } else if (params.type === '用户收益') {
    url = '/profitDistribution/profit/getResourceUserExcel';
  } else if (params.type === '交易详情收益') {
    url = '/profitDistribution/profit/getTransactionDetailExcel';
  }
  if (mock) {
    return new Promise((resolve) => {
      resolve(true);
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get('/' + api + url, {
          responseType: 'blob',
        })
        .then((res) => {
          console.log(res);
          const data = res.data;
          const blob = new Blob([data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          });
          let u = window.URL.createObjectURL(blob);
          let aLink = document.createElement('a');
          aLink.style.display = 'none';
          aLink.href = u;
          aLink.setAttribute('download', params.type + '报表.xls');
          document.body.appendChild(aLink);
          aLink.click();
          document.body.removeChild(aLink);
          window.URL.revokeObjectURL(u);
          resolve(true);
        });
    });
  }
};
export const AGetDateTable = (params?: {
  current: number;
  pageSize: number;
  dateStr: string;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: mockDateTable,
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get('/' + api + '/report2F/dateTableValue', {
        params,
      })
      .then((res) => {
        resolve(res.data.data);
      });
  });
};
export const AGetPriceTable = (params?: {
  dateType: 'First' | 'realTime';
  current: number;
  pageSize: number;
  monthStr: string;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      mockPriceTable.forEach((i) => {
        i.data.forEach((d, index) => {
          i[index] = d;
        });
      });
      resolve({
        list: mockPriceTable,
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get('/' + api + '/report2F/electricityPrice', {
        params,
      })
      .then((res) => {
        res?.data?.data?.list?.forEach((i) => {
          i.data.forEach((d, index) => {
            i[index] = d;
          });
        });
        resolve(res.data.data);
      });
  });
};
export const AGetIncomeTable = (params?: {
  dateType: 'vol' | 'revenue';
  current: number;
  pageSize: number;
  monthStr: string;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      mockIncomeTable.forEach((i) => {
        i.data.forEach((d, index) => {
          i[index] = d;
        });
      });
      resolve({
        list: mockIncomeTable,
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get('/' + api + '/report2F/electricitySales', {
        params,
      })
      .then((res) => {
        res?.data?.data?.list?.forEach((i) => {
          i.data.forEach((d, index) => {
            i[index] = d;
          });
        });
        resolve(res.data.data);
      });
  });
};
export const AGetMedLongTable = (params?: {
  current: number;
  pageSize: number;
  dateStr: string;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: mockDateTable,
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get('/' + api + '/report2F/medAndLongTerm', {
        params,
      })
      .then((res) => {
        resolve(res.data.data);
      });
  });
};
export const AGetSumTable = (params?: {
  current: number;
  pageSize: number;
  monthStr: string;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        list: mockSumTable,
      });
    });
  }
  return new Promise((resolve) => {
    axios
      .get('/' + api + '/report2F/generalStatistical', {
        params,
      })
      .then((res) => {
        resolve(res.data.data);
      });
  });
};
