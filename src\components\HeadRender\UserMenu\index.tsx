import Report from '@/components/Report';
import UserManage from '@/components/UserManage';
import { history, useModel } from '@umijs/max';
import { Popconfirm } from 'antd';
import { useState } from 'react';
import styles from '../index.sass';

export default function () {
  const {
    initialState: { permission },
  } = useModel('@@initialState');
  const type = permission.type;
  const [showUserDetail, setShowUserDetail] = useState(false);
  const [openUser, setOpenUser] = useState(false);
  const [openReport, setOpenReport] = useState(false);
  const onExit = () => {
    localStorage.setItem('token', '');
    location.href = '/alogin';
  };
  const description = () => {
    return <div>确定退出登录？</div>;
  };
  const onClickUser = () => {
    setShowUserDetail(!showUserDetail);
  };
  return (
    <div className={styles.user}>
      <img
        className={styles.userIcon}
        onClick={onClickUser}
        src="/per4.png"
      ></img>
      <div className={styles.userName} onClick={onClickUser}></div>
      <div
        className={styles.loginPop}
        style={{
          display: showUserDetail ? '' : 'none',
        }}
      >
        {permission.province.includes('') && type !== '审计员' && (
          <div
            className={styles.userItem}
            onClick={() => {
              setShowUserDetail(false);
              history.push('/resource-management/user-detail');
            }}
          >
            <img src="/homeuser.png" className={styles.iconimg} alt="" />
            用户资源
          </div>
        )}
        {type === '管理员' && (
          <div
            className={styles.userItem}
            onClick={() => {
              setOpenUser(true);
            }}
          >
            <img src="/usermsg.png" className={styles.iconimg} alt="" />
            账号管理
          </div>
        )}
        {type === '审计员' && (
          <div
            className={styles.userItem}
            onClick={() => {
              setOpenReport(true);
            }}
          >
            <img src="/rizhi.png" className={styles.iconimg} alt="" />
            查看日志
          </div>
        )}
        <Popconfirm
          title="提示"
          description={description}
          okText="确定"
          cancelText="取消"
          onConfirm={onExit}
          onCancel={() => setShowUserDetail(false)}
        >
          <div className={styles.userItem}>
            <img src="/homeexit.png" className={styles.iconimg} alt="" />
            退出登录
          </div>
        </Popconfirm>
      </div>
      <UserManage
        open={openUser}
        onClose={() => setOpenUser(false)}
      ></UserManage>
      <Report open={openReport} onClose={() => setOpenReport(false)}></Report>
    </div>
  );
}
