import { data as sortData } from '@/pages/ResourceMonitor/components/EnergySort/mock';
import { data as statusData } from '@/pages/ResourceMonitor/components/Status/Detail/mock';
import { data as warningData } from '@/pages/ResourceMonitor/components/WarningTable/mock';
import { Response } from '@/typing';

import { loaction } from '@/pages/ResourceMonitor/components/CustomMap/mock';
import {
  ICoreIndicatorData,
  IDailyPower,
  IEnergySort,
  IMachineStatus,
  INewEnergy,
  INewMessage,
  IPowerInfo,
  IRegulateData,
  IResourceCount,
  IResourceStatus,
  IStatusData,
  ITrade,
  IWarningData,
  IWarningList,
} from '@/pages/ResourceMonitor/typing';
import { mock } from '@/utils/util';
import axios from 'axios';
const api = 'resourceMonitor';
/**
 * 资源概况
 * @returns
 */
export const AGetResourceInfo = (params: {
  city?: string; // 城市名称
}): Promise<Response<{ list: IResourceCount[]; cpacityUnit: string }>> => {
  return new Promise((resolve) => {
    axios
      .get<Response<{ list: IResourceCount[]; cpacityUnit: string }>>(
        '/' + api + '/dataMonitor/allResourceRunningStatistics/getBranchCount',
        { params },
      )
      .then((res) => {
        resolve({
          data: {
            list: res.data.data.list,
            cpacityUnit: res.data.data.cpacityUnit,
          },
        });
      });
  });
};
/**
 * 核心指标
 * @returns
 */
export const AGetCoreIndex = (params?: {
  city?: string;
}): Promise<Response<ICoreIndicatorData>> => {
  return new Promise((resolve) => {
    axios
      .get<Response<ICoreIndicatorData>>(
        '/' +
          api +
          '/dataMonitor/allResourceRunningStatistics/getCoreIndicators',
        { params },
      )
      .then((res) => {
        resolve(res.data);
      })
      .catch((error) => {
        console.error('获取核心指标失败:', error);
      });
  });
};

/**
 * 通讯状态
 * @returns
 */
export const AGetMachineStatus = (params: {
  city: string; // 城市名称
}): Promise<Response<IMachineStatus[]>> => {
  return new Promise((resolve) => {
    axios
      .get<Response<{ list: IMachineStatus[] }>>(
        '/' +
          api +
          '/dataMonitor/allResourceRunningStatistics/getEveryCommunicateStatusCount',
        { params },
      )
      .then((res) => {
        resolve({ data: res.data.data.list });
      });
  });
};
/**
 * 调控能力
 * @returns
 */
export const AGetRegulate = (): Promise<Response<string>> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: '362/1000MWh' });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<{ data: string }>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getWholeAdjustableCapability',
        )
        .then((res) => {
          resolve(res.data.data);
        });
    });
  }
};
export const AGetRegulateV2 = (): Promise<IRegulateData[]> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve([
        {
          name: '楼宇可调负荷',
          capacity: 1000,
          responseTime: 1569,
        },
        {
          name: '用户侧储能',
          capacity: 13648,
          responseTime: 6569,
        },
        {
          name: '充电桩',
          capacity: 10009,
          responseTime: 4269,
        },
      ]);
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<{ list: IRegulateData[] }>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getBranchAdjustableCapacity',
        )
        .then((res) => {
          resolve(res.data.data.list);
        });
    });
  }
};
/**
 * 通信状态
 * @returns
 */
export const AGetStatus = (): Promise<{ data: IResourceStatus }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          outline: 135,
          warning: 5,
          online: 300,
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<IResourceStatus>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getEveryCommunicateStatusCount',
        )
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};
/**
 * 每个状态对应的设备详情
 * @param status
 * @returns
 */
export const AGetStatusList = (params: {
  status: string;
  pageSize: number;
  current: number;
}): Promise<{ list: IStatusData[] }> => {
  console.log(params);
  if (mock) {
    return new Promise((resolve) => {
      resolve({ list: statusData });
    });
  } else {
    return new Promise((resolve) => {
      resolve({ list: statusData });
    });
    // return new Promise((resolve) => {
    //   axios
    //     .get<Response<IWarningList>>(
    //       '/' +
    //         api +
    //         '/dataMonitor/allResourceRunningStatistics/getEveryResourceByCommunicateStatus',
    //       { params },
    //     )
    //     .then((res) => {
    //       resolve(res.data.data);
    //     });
    // });
  }
};
/**
 * 告警数据
 * @returns
 */
export const AGetWarningList = (params: {
  current: number;
  pageSize: number;
}): Promise<IWarningList> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({ list: warningData });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<IWarningList>>(
          '/' + api + '/dataMonitor/resourceWarn/page',
          { params },
        )
        .then((res) => {
          resolve(res.data.data);
        });
    });
  }
};
/**
 * 预警处理
 * @returns
 */
export const AGetWarningData = (params: {
  warnId: number;
}): Promise<IWarningData> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        dealStatus: '已处理',
        dealTime: '2023/02/03 08:00:00',
        person: '王工',
        way: '现场处理',
        endTime: '2023/02/03 08:00:00',
        phone: 'xxxxxx',
        remark: '',
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<{ resourceWarn: IWarningData }>>(
          '/' + api + '/dataMonitor/resourceWarn/' + params.warnId,
          { params },
        )
        .then((res) => {
          resolve(res.data.data.resourceWarn);
        });
    });
  }
};
export const AUpdateWarningData = (params: {
  content: string;
  currentStatus: string;
  dealStatus: string;
  dealTime: string;
  endTime: string;
  level: string;
  param: string;
  person: string;
  phone: string;
  remark: string;
  resourceId: number;
  resourceName: string;
  time: string;
  warnId: number;
  way: string;
}): Promise<boolean> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve(true);
    });
  } else {
    return new Promise((resolve) => {
      axios
        .put<Response<IWarningList>>(
          '/' + api + '/dataMonitor/resourceWarn/' + params.warnId,
          params,
        )
        .then(() => {
          resolve(true);
        });
    });
  }
};
/**
 * 发用电信息
 * @param timeRange
 * @returns
 */
export const AGetPower = (params: {
  startTime: string;
  endTime: string;
}): Promise<{ data: IPowerInfo }> => {
  if (mock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            generatePower: '155',
            usePower: '123',
          },
        });
      }, 1000);
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<IPowerInfo>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getWholeGeneratePowerAndUsePower',
          { params },
        )
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};
/**
 * 能效排行
 * @param param
 * @returns
 */
export const AEnergySort = (params?: {
  stringStr: string;
}): Promise<{ data: IEnergySort[] }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({ data: sortData });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<{ list: IEnergySort[] }>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getEnergyEfficiencyRankList',
          { params },
        )
        .then((res) => {
          resolve({ data: res.data.data.list });
        });
    });
  }
};
/**
 * 新能源消纳情况
 * @param startTime
 * @param endTime
 * @returns
 */
export const AGetNewEnergy = (params: {
  startTime: string;
  endTime: string;
}): Promise<{ data: INewEnergy }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          power: '155MWh',
          use: '80%',
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<INewEnergy>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getNewEnergyConsume',
          { params },
        )
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

export const AGetDailyPower = (params: {
  timeString: string;
}): Promise<{ data: IDailyPower }> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          quarterList: ['1'],
          quarterElectricityQuantityList: [1],
        },
      });
    });
  } else {
    axios
      .get<Response<IDailyPower>>(
        '/' + api + '/dataMonitor/keZhong/getDayQuantityEveryDaySum',
        { params },
      )
      .then((res) => {
        resolve(res.data);
        console.log('日电量数据', res.data);
      });
  }
};

export const AGetResourceLocations = () => {
  if (mock) {
    return new Promise((resolve) => {
      resolve(loaction);
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getResourceLocations',
        )
        .then((res) => {
          resolve(res.data.data);
        });
    });
  }
};
export const ASendInfo = (params: {
  phoneNumbers: string;
  grade: string;
  resources: string;
  time: string;
  meg: string;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve(true);
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>(
          '/' + api + '/dataMonitor/allResourceRunningStatistics/sendMessage',
          { params },
        )
        .then((res) => {
          resolve(res.data.data);
        });
    });
  }
};
export const AGetMessage = (params: {
  current: number;
}): Promise<INewMessage> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        total: 1,
        current: 1,
        pageSize: 10,
        lastInfo: {
          id: 1,
          time: '2024-08-19 14:29:00',
          deadTime: '2024-08-19 15:09:00',
          message:
            ' 2024-08-19 14:29:00  福建省-南平市:发布了最新的需求响应信息（截至时间为2024-08-19 15:09:00），电网35千伏及以上市场用户参与需求响应，单个电力用户最小响应能力（最小可削减负荷）不低于200千瓦，响应时长不低于1小时。负荷集成商（现阶段由售电公司注册）可聚合其所代理的用户参与交易，最小响应能力不低于1000千瓦，响应时长不低于1小时。',
          province: '福建省',
        },
        list: [
          {
            id: 1,
            time: '2024-08-19 14:29:00',
            deadTime: '2024-08-19 15:09:00',
            message:
              ' 2024-08-19 14:29:00  福建省-南平市:发布了最新的需求响应信息（截至时间为2024-08-19 15:09:00），电网35千伏及以上市场用户参与需求响应，单个电力用户最小响应能力（最小可削减负荷）不低于200千瓦，响应时长不低于1小时。负荷集成商（现阶段由售电公司注册）可聚合其所代理的用户参与交易，最小响应能力不低于1000千瓦，响应时长不低于1小时。',
            province: '福建省',
          },
        ],
        isShow: false,
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getStateGridCorporationInfo',
          {
            params: {
              current: params.current || 1,
              pageSize: 4,
            },
          },
        )
        .then((res) => {
          resolve(res.data.data);
        });
    });
  }
};
export const AGetResourceType = () => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          list: ['分布式光伏', '储能', '分散式风电'],
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getSonResourceTypes',
        )
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};
export const AGetRunLine = (params: { city?: string; unitNum: string }) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          list: [
            {
              unitType: '#1',
              label: '发电曲线',
              timeList: ['00:00', '01:00'],
            },
          ],
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getUnitOperatingCurve',
          {
            params: {
              ...params,
              interval: 60,
            },
          },
        )
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};
export const AGetTradeInfo = () => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          list: [
            {
              unitType: '#1',
              marketPrice: 316.48,
              predictPrice: 322.24,
              profitSum: 10.35,
              tradeCount: 23,
              day: '2025-01-03',
              tradeType: '报量不报价',
              tradeState: '完成',
            },
            {
              unitType: '#2R',
              marketPrice: 339.88,
              predictPrice: 402.24,
              profitSum: 19.35,
              tradeCount: 43,
              day: '2025-03-19',
              tradeType: '报量不报价',
              tradeState: '竞价中',
            },
            {
              unitType: '#2F',
              marketPrice: 327.88,
              predictPrice: 416.13,
              profitSum: 11.72,
              tradeCount: 21,
              day: '2025-03-19',
              tradeType: '报量报价',
              tradeState: '竞价中',
            },
          ],
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>('/' + api + '/dataMonitor/tradeInfo/getNewestInfo')
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};
export const AGetCountPrice = (params: { city?: string; timeType: string }) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          list: [
            {
              unitType: '#1',
              adjustVolume: 0,
              generation: 0,
              profit: 0,
            },
            {
              unitType: '#2R',
              adjustVolume: 0,
              generation: 0,
              profit: 0,
            },
            {
              unitType: '#2F',
              adjustVolume: 0,
              generation: 0,
              profit: 0,
            },
          ],
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getTimeSequenceInfo',
          { params: { timeType: '年', ...params } },
        )
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};
export const AGetWarningInfo = (params: { city?: string }) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          list: [
            {
              warningType: '调控执行率低',
              count: 10,
            },
            {
              warningType: '电流过大',
              count: 2,
            },
            {
              warningType: '设备故障',
              count: 9,
            },
            {
              warningType: '温度过高',
              count: 3,
            },
            {
              warningType: '电压过大',
              count: 4,
            },
          ],
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getWarningInfo',
          { params },
        )
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};
export const AGetResponseCount = (params: {
  city?: string;
  timeType: string;
}) => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          responseDiffRate: '9.27%',
          responseCount: 14,
          responseCompleteRate: '72.24%',
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>(
          '/' +
            api +
            '/dataMonitor/allResourceRunningStatistics/getResponseInfo',
          { params },
        )
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

export const AGetTrade = (params: { current: number }): Promise<ITrade> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        total: 1,
        current: 1,
        pageSize: 10,
        list: [
          {
            id: 1,
            unitType: '#1',
            day: '2025-01-03',
            tradeType: '报量不报价',
            tradeVolume: 23,
            marketPrice: 316.48,
            profitSum: 10.35,
            tradeState: '完成',
          },
        ],
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get('/' + api + '/dataMonitor/tradeInfo/page', {
          params: {
            current: params.current || 1,
            pageSize: 4,
          },
        })
        .then((res) => {
          resolve(res.data.data);
        });
    });
  }
};

export const AGetTradeType = () => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          list: ['报量不报价', '报量报价'],
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>('/' + api + '/dataMonitor/tradeInfo/getTradeTypes')
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

export const AGetTradeState = () => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          list: ['完成', '未开展', '竞价中'],
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>('/' + api + '/dataMonitor/tradeInfo/getTradeStates')
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

export const AGetUnitType = () => {
  if (mock) {
    return new Promise((resolve) => {
      resolve({
        data: {
          list: ['#1', '#2F', '#2R'],
        },
      });
    });
  } else {
    return new Promise((resolve) => {
      axios
        .get<Response<any>>(
          '/' + api + '/dataMonitor/allResourceRunningStatistics/getUnitTypes',
        )
        .then((res) => {
          resolve(res.data);
        });
    });
  }
};

export const AAddTradeInfo = (params: {
  day: 'string';
  marketPrice: 0;
  profitSum: 0;
  tradeState: 'string';
  tradeType: 'string';
  tradeVolume: 0;
  unitType: 'string';
  skipInterceptor?: boolean;
}): Promise<boolean> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve(true);
    });
  } else {
    return new Promise((resolve) => {
      const { skipInterceptor, ...restParams } = params;
      axios
        .post<Response<any>>('/' + api + '/dataMonitor/tradeInfo', restParams, {
          headers: {
            'X-Skip-Interceptor': skipInterceptor ? 'true' : 'false',
          },
        })
        .then(() => {
          resolve(true);
        });
    });
  }
};

export const AUpdateTrade = (params: {
  id: 0;
  day: 'string';
  marketPrice: 0;
  profitSum: 0;
  tradeState: 'string';
  tradeType: 'string';
  tradeVolume: 0;
  unitType: 'string';
  skipInterceptor?: boolean;
}): Promise<boolean> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve(true);
    });
  } else {
    return new Promise((resolve) => {
      const { id, skipInterceptor, ...restParams } = params;
      axios
        .put<Response<boolean>>(
          '/' + api + '/dataMonitor/tradeInfo/' + id,
          restParams,
          {
            headers: {
              'X-Skip-Interceptor': skipInterceptor ? 'true' : 'false',
            },
          },
        )
        .then(() => {
          resolve(true);
        });
    });
  }
};

export const ADeleteTrade = (params: { id: number }): Promise<boolean> => {
  if (mock) {
    return new Promise((resolve) => {
      resolve(true);
    });
  } else {
    return new Promise((resolve) => {
      axios
        .delete<Response<any>>(
          '/' + api + '/dataMonitor/tradeInfo/' + params.id,
        )
        .then(() => {
          resolve(true);
        });
    });
  }
};

export const AGetAdjustRollingInfos = () => {
  return new Promise((resolve) => {
    axios
      .get<Response<any>>(
        '/' +
          api +
          '/dataMonitor/allResourceRunningStatistics/getAdjustRollingInfos',
      )
      .then((res) => {
        resolve(res.data);
      });
  });
};

export const AGetDeviceOnlineRollingInfos = () => {
  return new Promise((resolve) => {
    axios
      .get<Response<any>>(
        '/' +
          api +
          '/dataMonitor/allResourceRunningStatistics/getDeviceOnlineRollingInfos',
      )
      .then((res) => {
        resolve(res.data);
      });
  });
};

export const AGenerateRecallParamsEfile = (params: {
  startDay: string;
  endDay: string;
}) => {
  return new Promise((resolve) => {
    axios
      .get<Response<any>>(
        '/' +
          api +
          '/dataMonitor/teldHistoryRecallController/generateRecallParamsEfile',
        { params },
      )
      .then((res) => {
        resolve(res.data);
      });
  });
};
