import CustomButton from '@/components/CustomButton';
import CustomDate from '@/components/CustomDate';
import CustomTableV2 from '@/components/CustomTableV2';
import { ADownLoadExcel, AGetSumTable } from '@/services/settlementAllocation';
import px from '@/utils/px';
import { useAntdTable } from 'ahooks';
import { Form, message } from 'antd';
import Table, { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import { IResourceBenefitData } from '../../typing';
import styles from './index.sass';

interface Props {
  name?: string;
}
const Index: React.FC<Props> = (props: Props) => {
  const {} = props;
  const container = useRef<HTMLDivElement>(null);

  const [form] = Form.useForm();
  const getTableData = (
    params: { pageSize: number; current: number },
    formData?: any,
  ) => {
    const { pageSize, current } = params;
    const { time } = formData || {};
    return AGetSumTable({
      pageSize,
      current,
      monthStr: time.format('YYYY-MM'),
    });
  };

  const { tableProps, search } = useAntdTable(getTableData, {
    form,
    defaultParams: [{ current: 1, pageSize: 20 }],
    defaultType: 'advance',
  });

  const { submit, reset } = search;

  const columns: ColumnsType<IResourceBenefitData> = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '全网分摊费用',
      dataIndex: 'gridCostSharing',
      key: 'gridCostSharing',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '公司分摊费用',
      dataIndex: 'companyCostSharing',
      key: 'companyCostSharing',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '日前申报回收返还',
      dataIndex: 'declarationReturn',
      key: 'declarationReturn',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '售电收入',
      dataIndex: 'electricityIncome',
      key: 'electricityIncome',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '购电支出',
      dataIndex: 'electricityPurchase',
      key: 'electricityPurchase',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '收益',
      dataIndex: 'revenue1',
      key: 'revenue1',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '收益（考虑分摊、回收返还）',
      dataIndex: 'revenue2',
      key: 'revenue2',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '售电单价',
      dataIndex: 'salesUnit',
      key: 'salesUnit',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '购电电价',
      dataIndex: 'purchaseUnit',
      key: 'purchaseUnit',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '购销价差',
      dataIndex: 'differencePrice1',
      key: 'differencePrice1',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '购销价差（考虑分摊、回收返还）',
      dataIndex: 'differencePrice2',
      key: 'differencePrice2',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '折合度电分摊',
      dataIndex: 'perAllocation',
      key: 'perAllocation',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '中长期覆盖率',
      dataIndex: 'coverageRate',
      key: 'coverageRate',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '实际电量',
      dataIndex: 'date',
      key: 'date',
      align: 'center',
      render: (v) => v || '-',
    },
    {
      title: '度电分摊',
      dataIndex: 'date',
      key: 'date',
      align: 'center',
      render: (v) => v || '-',
    },
  ];

  return (
    <>
      <div className={styles.box} ref={container}>
        <Form
          className={styles.form}
          form={form}
          initialValues={{
            time: dayjs('2025-07'),
          }}
        >
          <Form.Item name="time" label="选择日期">
            <CustomDate
              className={styles.date}
              allowClear={false}
              picker="month"
            ></CustomDate>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={submit}>
              筛选
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={reset}>
              重置
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton
              className={styles.button}
              onClick={() => {
                ADownLoadExcel({ type: '资源收益' }).then(() => {
                  message.success('下载成功！');
                });
              }}
            >
              下载
            </CustomButton>
          </Form.Item>
        </Form>
        <div className={styles.table}>
          <CustomTableV2
            {...tableProps}
            bordered
            columns={columns}
            scroll={{ x: px(2500), y: px(595) }}
            pagination={false}
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>当日总计</Table.Summary.Cell>
                  <Table.Summary.Cell index={1}></Table.Summary.Cell>
                </Table.Summary.Row>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>当月累计</Table.Summary.Cell>
                  <Table.Summary.Cell index={1}></Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            )}
          ></CustomTableV2>
        </div>
      </div>
    </>
  );
};

export default Index;
