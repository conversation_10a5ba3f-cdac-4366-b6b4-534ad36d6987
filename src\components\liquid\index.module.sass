.container
    margin: auto
    position: relative
    width: 100%
    height: 100%
    background-color: rgb(109,153,140)
    box-shadow: inset 0 0 50px #1c637c
    border-radius: 50%
    overflow: hidden

.before
    content: " "
    width: 200%
    height: 200%
    background-color: rgba(109,153,140,0.1)
    position: absolute
    top: -150%
    left: -50%
    border-radius: 40%
    animation: anim 12s linear infinite

.after
    content: ""
    width: 204%
    height: 204%
    background-color: #ececec80
    position: absolute
    top: -154%
    left: -52%
    border-radius: 40%
    animation: anim 12s linear infinite
    animation-delay: 0.5s
.label
    margin-left: 50%
    margin-top: 50%
    transform: translate(-50%, -50%)
    width: fit-content

@keyframes anim
    0%
        transform: rotate(0deg)

    100%
        transform: rotate(360deg)
