import styles from './index.module.sass';
const MonitorItem = ({ title, num, color, unit, iconUrl }) => {
  return (
    <div className={styles.container}>
      {iconUrl && (
        <div
          className={styles.left}
          style={{ backgroundImage: `url(${iconUrl})` }}
        ></div>
      )}
      <div className={styles.right}>
        <div className={styles.title}>{title}</div>
        <div className={styles.info}>
          <div className={styles.num} style={{ color: color }}>
            {' '}
            {num}
          </div>
          <div className={styles.unit}> {unit}</div>
        </div>
      </div>
    </div>
  );
};
export default MonitorItem;
