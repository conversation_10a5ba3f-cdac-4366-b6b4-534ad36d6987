import px from '@/utils/px';

export const option = (data: any) => {
  return {
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      top: '10%',
      left: '1%',
      right: px(20),
      bottom: '2%',
      containLabel: true,
    },
    legend: {
      data: ['日前电价', '用户侧实时电价(临时)', '用户侧实时电价(正式)'],
      textStyle: {
        color: '#fff',
        fontSize: px(16),
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: {
        fontSize: px(18),
        color: '#fff',
      },
      data: data.timeStrList,
    },
    yAxis: {
      type: 'value',
      name: '元/MWh',
      nameTextStyle: {
        fontSize: px(18),
        color: '#fff',
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255,255,255,0.1)',
        },
      },
      axisLabel: {
        fontSize: px(18),
        color: '#fff',
      },
    },
    dataZoom: {
      type: 'inside',
      start: 0,
      end: 100,
    },
    series: [
      {
        name: '日前电价',
        data: data.dayBeforePriceList,
        type: 'line',
        symbol: 'circle',
        showAllSymbol: true,
        symbolSize: 0,
        smooth: true,
        itemStyle: {
          color: '#F2BA02',
        },
        lineStyle: {
          color: '#F2BA02',
          width: px(3),
        },
      },
      {
        name: '用户侧实时电价(临时)',
        data: data.realTimePriceTempList,
        type: 'line',
        symbol: 'none',
        showAllSymbol: false,
        symbolSize: 0,
        smooth: true,
        itemStyle: {
          color: '#FF0000',
        },
        lineStyle: {
          color: '#FF0000',
          width: px(3),
        },
      },
      {
        name: '用户侧实时电价(正式)',
        data: data.realTimePriceNormalList,
        type: 'line',
        symbol: 'circle',
        showAllSymbol: true,
        symbolSize: 0,
        smooth: true,
        itemStyle: {
          color: '#4874CB',
        },
        lineStyle: {
          color: '#4874CB',
          width: px(3),
        },
      },
    ],
  };
};
