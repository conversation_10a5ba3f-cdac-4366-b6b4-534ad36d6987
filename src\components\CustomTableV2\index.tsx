import px from '@/utils/px';
import { ConfigProvider, Table, TableProps } from 'antd'; // 注意这里导入了TableProps类型
import React from 'react';
import styles from './index.sass';
const CustomTable: React.FC<TableProps<any>> = (props) => {
  return (
    <ConfigProvider
      theme={{
        token: { fontSize: px(18), controlHeight: px(35) },
        components: {
          Table: {
            padding: px(5),
            colorBgContainer: '#0E4280',
            headerBg: '#0E4280',
            borderColor: '#1C72E2',
            colorText: '#fff',
            colorTextHeading: '#fff',
            lineType: 'dashed',
            cellPaddingBlock: px(12),
            stickyScrollBarBg: '#999',
          },
          Pagination: {
            colorText: '#fff',
            itemBg: 'rgba(0,0,0,0)',
            colorBgContainer: 'rgba(0,0,0,0)',
            colorPrimary: '#fff',
            colorTextDisabled: '#999',
          },
        },
      }}
    >
      <Table className={styles.box + ' ' + props?.className} {...props} />
    </ConfigProvider>
  );
};

export default CustomTable;
