import {
  defalutTime,
  defaultSearch,
  myLocale,
  rangePresets,
} from '@/utils/util';
import { SearchOutlined } from '@ant-design/icons';
import { DatePicker, Select } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import { useState } from 'react';
import CustomDateV2 from '../CustomDateV2';
import styles from './index.sass';
import { ITabOptions, OptionType } from './typing';
dayjs.extend(weekday);
dayjs.extend(localeData);
const { RangePicker } = DatePicker;

interface Props {
  title?: string;
  onSearch?: (data: ITabOptions) => void;
  optionTypes?: Array<OptionType>;
  defaultTimeRange?: Dayjs[];
}

export default function (props: Props) {
  const { title, optionTypes, onSearch, defaultTimeRange } = props;
  const [optionValue, setOptionValue] = useState(defaultSearch);
  const handleRangeChange = (
    dates: null | (Dayjs | null)[],
    dateStrings: string[],
  ) => {
    if (dateStrings) {
      // onTimeChange?.(dateStrings);
      setOptionValue((origin) => {
        origin.time = dateStrings;
        return origin;
      });
    } else {
      console.log('Clear');
    }
  };
  const handleSearch = () => {
    onSearch?.(optionValue);
  };
  const handleSourceChange = (value: string) => {
    // onSourceChange?.(value);
    setOptionValue((origin) => {
      origin.resource = value;
      return origin;
    });
  };
  const handleRegulateChange = (value: string) => {
    // onRegulateChange?.(value);
    setOptionValue((origin) => {
      origin.regulate = value;
      return origin;
    });
    onSearch?.(optionValue);
  };
  const handleDateChange = (value: Dayjs) => {
    setOptionValue((origin) => {
      origin.date = value.format('YYYY-MM-DD HH:mm:ss');
      return origin;
    });
  };
  return (
    <div className={styles.box}>
      <div className={styles.title}>{title}</div>
      <div className={styles.options}>
        {optionTypes?.includes('source') && (
          <Select
            onChange={handleSourceChange}
            className={styles.select}
            defaultValue=""
            options={[
              { value: '', label: '全部资源类型' },
              { value: '充电桩', label: '充电桩' },
              { value: '分布式光伏发电', label: '分布式光伏发电' },
              { value: '用户侧储能', label: '用户侧储能' },
              { value: '楼宇可调负荷', label: '楼宇可调负荷' },
              { value: '数据中心', label: '数据中心' },
              { value: '冷热三联供', label: '冷热三联供' },
            ]}
          />
        )}
        {optionTypes?.includes('regulate') && (
          <Select
            onChange={handleRegulateChange}
            className={styles.select}
            defaultValue="可调容量"
            options={[
              { value: '可调容量', label: '可调容量/MW' },
              { value: '响应时间', label: '响应时间/秒' },
            ]}
          />
        )}
        {optionTypes?.includes('date') && (
          <RangePicker
            allowClear={false}
            locale={myLocale}
            className={styles.rangeDate}
            presets={rangePresets}
            format="YYYY/MM/DD"
            defaultValue={defaultTimeRange || defalutTime}
            onChange={handleRangeChange}
          />
        )}
        {optionTypes?.includes('singleDate') && (
          <CustomDateV2
            allowClear={false}
            locale={myLocale}
            className={styles.date}
            presets={rangePresets}
            format="YYYY-MM-DD"
            defaultValue={defaultTimeRange?.[1] || defalutTime[1]}
            onChange={handleDateChange}
          />
        )}
        {optionTypes?.includes('mounth') && (
          <RangePicker
            picker="month"
            locale={myLocale}
            allowClear={false}
            className={styles.rangeDate}
            defaultValue={defaultTimeRange || defalutTime}
            onChange={handleRangeChange}
          />
        )}
        {!optionTypes?.includes('regulate') && optionTypes?.length && (
          <div className={styles.search} onClick={handleSearch}>
            <SearchOutlined />
          </div>
        )}
      </div>
    </div>
  );
}
