import CustomButton from '@/components/CustomButton';
import CustomDate from '@/components/CustomDate';
import CustomSelect from '@/components/CustomSelect';
import CustomTableV2 from '@/components/CustomTableV2';
import {
  ADownLoadExcel,
  AGetIncomeTable,
} from '@/services/settlementAllocation';
import px from '@/utils/px';
import { useAntdTable } from 'ahooks';
import { Form, message } from 'antd';
import Table, { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import { IResourceBenefitData } from '../../typing';
import styles from './index.sass';

interface Props {
  name?: string;
}
const Index: React.FC<Props> = (props: Props) => {
  const {} = props;
  const container = useRef<HTMLDivElement>(null);

  const [form] = Form.useForm();
  const getTableData = (
    params: { pageSize: number; current: number },
    formData?: any,
  ) => {
    const { pageSize, current } = params;
    const { type = 'realTime', time = dayjs('2025-07') } = formData || {};
    return AGetIncomeTable({
      dateType: type,
      monthStr: time.format('YYYY-MM'),
      pageSize,
      current,
    });
  };

  const { tableProps, search } = useAntdTable(getTableData, {
    form,
    defaultParams: [{ current: 1, pageSize: 20 }],
    defaultType: 'advance',
  });

  const { submit, reset } = search;
  // const { getFieldValue } = form;
  // 根据数据 生成列 展示对应列名
  const columns: ColumnsType<IResourceBenefitData> = [
    {
      title: '时段',
      dataIndex: 'period',
      key: 'period',
      align: 'center',
      fixed: 'left',
    },
    // {
    //   title: '单价',
    //   dataIndex: 'd',
    //   key: 'd',
    //   align: 'center',
    //   hidden: getFieldValue('type') === 'realTime',
    // },
  ];
  const d = tableProps.dataSource?.[0] || {};
  for (let i = 1; i < d.data?.length || 0; i++) {
    columns.push({
      title: d.month + '-' + i,
      dataIndex: i,
      key: i,
      render: (v) => v || '-',
    });
  }

  return (
    <>
      <div className={styles.box} ref={container}>
        <Form
          className={styles.form}
          form={form}
          initialValues={{
            type: 'vol',
            time: dayjs('2025-07'),
          }}
        >
          <Form.Item name="type" label="数据类型">
            <CustomSelect
              className={styles.select}
              options={[
                {
                  label: '电量(MWh)',
                  value: 'vol',
                },
                {
                  label: '售电收入(元)',
                  value: 'revenue',
                },
              ]}
            ></CustomSelect>
          </Form.Item>
          <Form.Item name="time" label="选择日期">
            <CustomDate
              allowClear={false}
              className={styles.date}
              picker="month"
            ></CustomDate>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={submit}>
              筛选
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={reset}>
              重置
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton
              className={styles.button}
              onClick={() => {
                ADownLoadExcel({ type: '资源收益' }).then(() => {
                  message.success('下载成功！');
                });
              }}
            >
              下载
            </CustomButton>
          </Form.Item>
        </Form>
        <div className={styles.table}>
          <CustomTableV2
            {...tableProps}
            bordered
            columns={columns}
            scroll={{ x: px(3000), y: px(620) }}
            pagination={false}
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>当日总计</Table.Summary.Cell>
                  <Table.Summary.Cell index={1}></Table.Summary.Cell>
                </Table.Summary.Row>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>当月累计</Table.Summary.Cell>
                  <Table.Summary.Cell index={1}></Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            )}
          ></CustomTableV2>
        </div>
      </div>
    </>
  );
};

export default Index;
