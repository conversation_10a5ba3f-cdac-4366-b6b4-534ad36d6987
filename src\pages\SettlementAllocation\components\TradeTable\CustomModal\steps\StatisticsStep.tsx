import px from '@/utils/px';
import { ConfigProvider, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React from 'react';
import styles from './index.sass';
interface StatisticsStepProps {
  data: any;
}

const StatisticsStep: React.FC<StatisticsStepProps> = ({ data }) => {
  // 第四步：收益统计表格列配置
  const columns: ColumnsType<any> = [
    {
      title: '统计项目',
      dataIndex: 'item',
      key: 'item',
      align: 'center',
    },
    {
      title: '数值',
      dataIndex: 'value',
      key: 'value',
      align: 'center',
      render: (value: any, record: any) => {
        return (
          <span
            style={{
              color: '#00D4FF',
              fontWeight: 'bold',
              fontSize: px(16),
            }}
          >
            {value} {record.unit}
          </span>
        );
      },
    },
  ];

  // 转换数据格式
  const statisticsData = data
    ? [
        {
          key: '1',
          item: '日期',
          value: data.dayStr,
          unit: '',
        },
        {
          key: '2',
          item: '整体调节量',
          value: data.wholeAdjustEnergy,
          unit: 'MW',
        },
        {
          key: '3',
          item: '特来电调节量',
          value: data.teldAdjustEnergy,
          unit: 'MW',
        },
        {
          key: '4',
          item: '电表调节量',
          value: data.meterAdjustEnergy,
          unit: 'MW',
        },
        {
          key: '5',
          item: '调节电价',
          value: data.adjustPrice,
          unit: '元/MWh',
        },
        {
          key: '6',
          item: '整体收益',
          value: data.wholeProfit,
          unit: '元',
        },
        {
          key: '7',
          item: '特来电收益',
          value: data.teldProfit,
          unit: '元',
        },
        {
          key: '8',
          item: '电表收益',
          value: data.meterProfit,
          unit: '元',
        },
        {
          key: '9',
          item: 'VPP收益',
          value: data.vppProfit,
          unit: '元',
        },
      ]
    : [];

  return (
    <div className={styles.stepContent}>
      <h3 className={styles.stepTitle}>收益统计</h3>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              stickyScrollBarBg: '#f0f0f0', // 设置为更明亮的颜色
            },
          },
        }}
      >
        <Table
          dataSource={statisticsData}
          columns={columns}
          className={styles.table}
          size="small"
          pagination={false}
          scroll={{ y: px(400) }}
        />
      </ConfigProvider>
    </div>
  );
};

export default StatisticsStep;
