import CustomButton from '@/components/CustomButton';
import CustomInput from '@/components/CustomInput';
import CustomModal from '@/components/CustomModal';
import CustomSelect from '@/components/CustomSelect';
import { AAddUser, AEditUser } from '@/services/login';
import { Form } from 'antd';
import React, { useEffect } from 'react';
import styles from './index.sass';
interface Props {
  userData?: any;
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const Index: React.FC<Props> = (props: Props) => {
  const { open, onCancel, userData, onSuccess } = props;
  const [form] = Form.useForm();
  const { validateFields, setFieldsValue, resetFields } = form;
  useEffect(() => {
    if (userData) {
      setFieldsValue({
        ...userData,
      });
    } else {
      resetFields();
    }
  }, [userData]);
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  const onTypeChange = (value: string) => {
    setFieldsValue({ type: value });
  };
  const onReset = () => {
    resetFields();
  };
  const onCheck = async () => {
    try {
      const values = await validateFields();
      if (userData?.uid) {
        AEditUser({
          ...values,
          id: userData.uid,
        }).then(() => {
          onSuccess();
        });
      } else {
        AAddUser({
          ...values,
        }).then(() => {
          onSuccess();
        });
      }
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
  };
  return (
    <CustomModal
      open={open}
      title="账号信息"
      onCancel={onCancel}
      footer={null}
      className={styles.box}
      width={'fit-content'}
      centered={true}
    >
      <Form form={form} name="form" className={styles.form}>
        <Form.Item
          {...formItemLayout}
          name="name"
          label="账号"
          rules={[{ required: true, message: '请输入账号' }]}
        >
          <CustomInput placeholder="请输入账号" />
        </Form.Item>
        {!userData && (
          <Form.Item
            {...formItemLayout}
            name="pwd"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' },
              {
                pattern: /^(?=.*\d)(?=.*[a-zA-Z]).{8,}$/,
                message: '密码至少为8位，且必须包含字母和数字！',
              },
            ]}
          >
            <CustomInput placeholder="请输入密码" />
          </Form.Item>
        )}
        <Form.Item
          {...formItemLayout}
          name="type"
          label="类型"
          rules={[{ required: true, message: '请输入类型' }]}
        >
          <CustomSelect
            placeholder="请选择类型"
            onChange={onTypeChange}
            options={[
              {
                label: '操作员',
                value: '操作员',
              },
              {
                label: '审计员',
                value: '审计员',
              },
            ]}
          />
        </Form.Item>
      </Form>
      <div className={styles.buttons}>
        <CustomButton className={styles.button} onClick={onCancel}>
          取消
        </CustomButton>
        <CustomButton className={styles.button} onClick={onReset}>
          重置
        </CustomButton>
        <CustomButton className={styles.button} onClick={onCheck}>
          确定
        </CustomButton>
      </div>
    </CustomModal>
  );
};

export default Index;
