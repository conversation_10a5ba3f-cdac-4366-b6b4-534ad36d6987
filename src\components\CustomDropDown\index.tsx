import { Button, Dropdown } from 'antd';
import { useState } from 'react';
import styles from './index.sass';
interface IProp {
  onClick: (params: { path: string }) => void;
  routerName: string;
}

const routerMap = {
  市场交易: [
    {
      name: '调节量交易',
      path: '/market-trading/market-trading',
    },
    {
      name: '中长期市场',
      path: '/market-trading/long-trading',
    },
    {
      name: '需求响应',
      path: '/market-trading/demand-response',
    },
    {
      name: '辅助服务',
      path: '/market-trading/auxiliary-service',
    },
  ],
  辅助决策: [
    {
      name: '预测分析',
      path: '/decision-support/load-forecasting',
    },
    {
      name: '对比分析',
      path: '/decision-support/comparison-analysis',
    },
    {
      name: '日报分析',
      path: '/decision-support/daily-report',
    },
    {
      name: '电价分析',
      path: '/decision-support/pricing-strategy',
    },
    {
      name: '调度计划',
      path: '/decision-support/scheduling-strategy',
    },
  ],
  首页: [
    {
      name: '首页',
      path: '/resource-monitor',
    },
  ],
  资源管理: [
    {
      name: '基本信息',
      path: '/resource-management',
    },
    // {
    //   name: '合同管理',
    //   path: '/contract-management',
    // },
    {
      name: '数据监测',
      path: '/resource-detail-monitor',
    },
  ],
};

const MyDropdown = (prop: IProp) => {
  const { onClick, routerName } = prop;
  const [text, setText] = useState(routerName);

  const items2 = routerMap[routerName].map((item) => {
    return {
      key: item.path,
      label: (
        <button
          type="button"
          onClick={() => {
            onClick({
              path: item.path,
            });
            setText(item.name);
          }}
          className={styles.btn}
        >
          {item.name}
        </button>
      ),
    };
  });

  return (
    <div className={styles.box}>
      <Dropdown
        trigger={['click']}
        menu={{ items: items2 }}
        overlayClassName={styles.menu}
        placement={'bottom'}
      >
        <Button
          className={styles.titleBtn}
          onClick={() => {
            setText(routerName);
          }}
        >
          {text}
        </Button>
      </Dropdown>
    </div>
  );
};

export default MyDropdown;
