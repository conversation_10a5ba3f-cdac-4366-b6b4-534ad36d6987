import CustomButton from '@/components/CustomButton';
import CustomModal from '@/components/CustomModal';
import {
  AGetBaseLineDays,
  AGetTotalCurves,
  ARecalBaseline,
  exportAdjust,
} from '@/services/resourceMointor/adjustLoadUnit';
import px from '@/utils/px';
import { DatePicker, Spin } from 'antd';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { useEffect, useRef, useState } from 'react';
import styles from './index.sass';
import { option } from './option';

export default function () {
  const container = useRef<HTMLDivElement>(null);
  const modalContainer = useRef<HTMLDivElement>(null);
  const myChart = useRef<any>();
  const modalChart = useRef<any>();
  const [date, setDate] = useState(dayjs());
  const [open, setOpen] = useState(false);
  const [day, setDay] = useState<string[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    AGetBaseLineDays({ dayStr: date.format('YYYY-MM-DD') }).then((res: any) => {
      setDay(res.baseLineDays);
    });
    if (!myChart.current && container.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
    }
    AGetTotalCurves({ dayStr: date.format('YYYY-MM-DD 00:00:00') }).then(
      (res) => {
        if (myChart.current) {
          myChart.current.setOption(option(res));
        }
      },
    );
  }, [date]);

  // 初始化Modal中的图表
  useEffect(() => {
    if (!open || !modalContainer.current) return;

    const initModalChart = () => {
      if (!modalChart.current) {
        modalChart.current = echarts.init(
          modalContainer.current as HTMLDivElement,
        );
      }

      AGetTotalCurves({ dayStr: date.format('YYYY-MM-DD 00:00:00') }).then(
        (res) => {
          if (modalChart.current) {
            modalChart.current.setOption(option(res));
            // 延迟一点时间调用resize以确保容器已经渲染完成
            setTimeout(() => {
              modalChart.current?.resize();
            }, 100);
          }
        },
      );
    };

    initModalChart();

    const handleResize = () => {
      modalChart.current?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [open, date]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (myChart.current) {
        myChart.current.dispose();
      }
      if (modalChart.current) {
        modalChart.current.dispose();
      }
    };
  }, []);

  return (
    <div className={styles.box}>
      <div className={styles.top}>
        <DatePicker value={date} onChange={setDate}></DatePicker>
        <div className={styles.btns}>
          <CustomButton
            style={{ marginRight: px(10) }}
            onClick={async () => {
              setLoading(true);
              try {
                await ARecalBaseline({ dayStr: date.format('YYYY-MM-DD') });
                // 重新计算完成后，重新获取数据
                const [baseLineDaysRes, totalCurvesRes] = await Promise.all([
                  AGetBaseLineDays({ dayStr: date.format('YYYY-MM-DD') }),
                  AGetTotalCurves({
                    dayStr: date.format('YYYY-MM-DD 00:00:00'),
                  }),
                ]);
                setDay(baseLineDaysRes.baseLineDays);
                if (myChart.current) {
                  myChart.current.setOption(option(totalCurvesRes));
                }
              } finally {
                setLoading(false);
              }
            }}
          >
            重新计算
          </CustomButton>
          <CustomButton
            style={{ marginRight: px(10) }}
            onClick={() => exportAdjust({ dayStr: date.format('YYYY-MM-DD') })}
          >
            调节量Execl导出
          </CustomButton>
          <CustomButton onClick={() => setOpen(true)}>放大</CustomButton>
        </div>
      </div>
      <div className={styles.tip}>
        <div className={styles.head} onClick={() => setIsExpanded(!isExpanded)}>
          <div className={styles.time}>基线计算日期</div>
          <div className={styles.expand}>{isExpanded ? '▼' : '▶'}</div>
        </div>
        {isExpanded &&
          day.map((d, index) => {
            return (
              <div key={index} className={styles.content}>
                {d}
              </div>
            );
          })}
      </div>
      <Spin spinning={loading} tip="计算中...">
        <div
          style={{
            width: '100%',
            height: px(270),
          }}
          ref={container}
        ></div>
      </Spin>
      <CustomModal
        width={px(1200)}
        onCancel={() => setOpen(false)}
        open={open}
        centered
        footer={[]}
      >
        <div
          style={{
            width: '100%',
            height: px(700),
          }}
          ref={modalContainer}
        ></div>
      </CustomModal>
    </div>
  );
}
